"""
结果整合器 - 专门负责格式化最终输出
"""

import re
import logging
from typing import Dict, Any, List, Optional
from dataclasses import dataclass

from .intent_analyzer import IntentResult, IntentType
from .tool_orchestrator import ToolResult

logger = logging.getLogger(__name__)


@dataclass
class FormattedResponse:
    """格式化的响应结果"""
    content: str
    has_tables: bool = False
    table_count: int = 0
    used_tools: List[str] = None
    processing_summary: str = ""


class ResponseFormatter:
    """
    结果整合器
    
    职责：
    1. 整合多个工具的输出结果
    2. 格式化最终答案
    3. 生成表格和可视化
    4. 应用业务规则和模板
    """
    
    def __init__(self):
        self.province_mapping = {
            "GX": "广西",
            "GZ": "广州", 
            "JT": "集团"
        }
    
    def format_final_response(self, tool_results: List[ToolResult],
                            intent: IntentResult,
                            matched_prompts: List = None) -> FormattedResponse:
        """
        格式化最终响应

        Args:
            tool_results: 工具执行结果
            intent: 意图分析结果
            matched_prompts: 匹配的复杂提示词列表
            tool_results: 工具执行结果列表
            intent: 意图分析结果
            
        Returns:
            FormattedResponse: 格式化的响应
        """
        logger.info(f"📝 [结果整合] 开始格式化响应，工具结果数: {len(tool_results)}")

        # 🎯 优先使用复杂提示词的格式化逻辑
        if matched_prompts and len(matched_prompts) > 0:
            logger.info(f"🎯 [结果整合] 使用复杂提示词指导响应格式化")
            # matched_prompts是(ComplexPrompt, similarity)的元组列表
            complex_prompt, similarity = matched_prompts[0]
            response = self._format_with_complex_prompt(tool_results, intent, complex_prompt)
        elif intent.intent_type == IntentType.COMPLEX_ANALYSIS:
            response = self._format_complex_analysis(tool_results, intent)
        elif intent.intent_type == IntentType.SIMPLE_QUERY:
            response = self._format_simple_query(tool_results, intent)
        else:
            response = self._format_default_response(tool_results, intent)
        
        # 添加处理摘要
        response.processing_summary = self._generate_processing_summary(tool_results)
        response.used_tools = [r.tool_call.tool_name for r in tool_results if r.tool_call]
        
        logger.info(f"✅ [结果整合] 响应格式化完成，包含表格: {response.has_tables}")
        return response
    
    def _format_complex_analysis(self, tool_results: List[ToolResult], 
                                intent: IntentResult) -> FormattedResponse:
        """格式化复杂分析结果"""
        
        # 获取省份名称
        province_name = self.province_mapping.get(intent.province_code, intent.province_code or "")
        
        # 构建响应内容
        content_parts = []
        
        # 标题
        content_parts.append(f"# 📊 {province_name}网络电费7月风险")
        content_parts.append("")
        
        # 总览部分
        overview_section = self._build_overview_section(tool_results)
        if overview_section:
            content_parts.extend(overview_section)
        
        # 明细部分
        detail_section = self._build_detail_section(tool_results)
        if detail_section:
            content_parts.extend(detail_section)
        
        # 建议部分
        suggestion_section = self._build_suggestion_section(tool_results)
        if suggestion_section:
            content_parts.extend(suggestion_section)
        
        content = "\n".join(content_parts)
        
        # 统计表格信息
        table_count = content.count("| --- |")
        has_tables = table_count > 0
        
        return FormattedResponse(
            content=content,
            has_tables=has_tables,
            table_count=table_count
        )
    
    def _format_simple_query(self, tool_results: List[ToolResult], 
                           intent: IntentResult) -> FormattedResponse:
        """格式化简单查询结果"""
        
        if not tool_results or not tool_results[0].success:
            return FormattedResponse(
                content="抱歉，查询失败。请稍后重试。",
                has_tables=False
            )
        
        result = tool_results[0].result
        
        # 如果结果包含表格，直接返回
        if isinstance(result, dict) and "summary" in result:
            content = result["summary"]
        else:
            content = str(result)
        
        # 统计表格信息
        table_count = content.count("| --- |")
        has_tables = table_count > 0
        
        return FormattedResponse(
            content=content,
            has_tables=has_tables,
            table_count=table_count
        )
    
    def _format_default_response(self, tool_results: List[ToolResult], 
                                intent: IntentResult) -> FormattedResponse:
        """默认响应格式化"""
        return self._format_simple_query(tool_results, intent)
    
    def _build_overview_section(self, tool_results: List[ToolResult]) -> List[str]:
        """构建总览部分"""
        # 查找第一个SQL查询结果（总体情况）
        overview_result = None
        for result in tool_results:
            if (result.success and result.tool_call and 
                result.tool_call.tool_name == "integrated_sql" and
                result.step_index == 0):
                overview_result = result
                break
        
        if not overview_result:
            return []
        
        section = []
        section.append("## 📋 费用风险分析总览：")
        section.append("")
        
        # 提取并格式化总览数据
        if isinstance(overview_result.result, dict):
            execution_result = overview_result.result.get("execution_result", {})
            data = execution_result.get("data", [])
            columns = execution_result.get("columns", [])
            
            if data and columns:
                # 生成总结文本
                total_records = len(data)
                section.append(f"2025年7月网络电费风险分析显示，共启用3项风险检查规则，发现{total_records}类风险问题。")
                section.append("")
                
                # 生成表格
                table_lines = self._generate_table(columns, data)
                section.extend(table_lines)
        
        return section
    
    def _build_detail_section(self, tool_results: List[ToolResult]) -> List[str]:
        """构建明细部分"""
        section = []
        section.append("## ⚠️ 费用风险分析明细：")
        section.append("")
        section.append("**重要说明：此部分展示各风险指标的详细查询结果**")
        section.append("")
        
        # 查找指标详情查询结果
        detail_results = [
            r for r in tool_results 
            if (r.success and r.tool_call and 
                r.tool_call.tool_name == "integrated_sql" and
                r.step_index > 0 and r.step_index < len(tool_results) - 1)
        ]
        
        for i, result in enumerate(detail_results):
            if isinstance(result.result, dict):
                execution_result = result.result.get("execution_result", {})
                data = execution_result.get("data", [])
                columns = execution_result.get("columns", [])
                
                if data and columns:
                    # 从工具调用参数中提取指标名称
                    indicator_name = self._extract_indicator_name(result.tool_call.parameters.get("question", ""))
                    
                    section.append(f"### {i+1}. {indicator_name}")
                    section.append("")
                    
                    # 生成明细表格
                    table_lines = self._generate_table(columns, data[:10])  # 限制显示行数
                    section.extend(table_lines)
                    
                    if len(data) > 10:
                        section.append(f"*注：共 {len(data)} 条记录，仅显示前 10 条*")
                        section.append("")
        
        return section
    
    def _build_suggestion_section(self, tool_results: List[ToolResult], complex_prompt=None) -> List[str]:
        """构建建议部分"""
        section = []
        section.append("## 💡 费用风险管理优化建议：")
        section.append("")
        
        # 查找知识搜索结果
        knowledge_result = None
        for result in tool_results:
            if (result.success and result.tool_call and 
                result.tool_call.tool_name == "knowledge_search"):
                knowledge_result = result
                break
        
        if knowledge_result and isinstance(knowledge_result.result, str):
            # 解析知识搜索结果
            suggestions = self._parse_knowledge_result(knowledge_result.result)
            section.extend(suggestions)
        else:
            # 默认建议
            section.append("**预付费超期未核销**是当前主要风险点，需重点关注。")
            section.append("")
            section.append("**优化建议：**")
            section.append("1. 建立预付费定期核销提醒机制")
            section.append("2. 完善发票管理流程，确保发票规范性")
            section.append("3. 加强合同单价管控，避免超标缴费")
            section.append("4. 定期开展风险排查和整改工作")
        
        return section
    
    def _generate_table(self, columns: List[str], data: List[Dict]) -> List[str]:
        """生成标准markdown表格"""
        if not columns or not data:
            return ["查询结果为空。", ""]
        
        table_lines = []
        
        # 清理列名
        clean_columns = [str(col).strip().replace('|', '').replace('\n', '') for col in columns]
        
        # 表头
        header = "| " + " | ".join(clean_columns) + " |"
        separator = "| " + " | ".join(["---"] * len(clean_columns)) + " |"
        
        table_lines.append(header)
        table_lines.append(separator)
        
        # 数据行
        for row in data:
            row_values = []
            for col in columns:
                value = row.get(col, "")
                # 清理值
                clean_value = str(value).replace('|', '｜').replace('\n', ' ').strip()
                if len(clean_value) > 30:
                    clean_value = clean_value[:27] + "..."
                row_values.append(clean_value)
            
            row_line = "| " + " | ".join(row_values) + " |"
            table_lines.append(row_line)
        
        table_lines.append("")  # 空行
        return table_lines
    
    def _extract_indicator_name(self, question: str) -> str:
        """从查询问题中提取指标名称"""
        # 简单的关键词匹配
        indicators = {
            "预付费": "预付费超期未核销",
            "转供电": "转供电非正规发票", 
            "缴费单": "已报账缴费单超过合同约定单价",
            "发票": "转供电非正规发票"
        }
        
        for keyword, indicator in indicators.items():
            if keyword in question:
                return indicator
        
        return "风险指标"
    
    def _parse_knowledge_result(self, knowledge_text: str) -> List[str]:
        """解析知识搜索结果"""
        suggestions = []
        
        # 简单解析，实际应该更智能
        if "预付费" in knowledge_text:
            suggestions.append("**预付费超期未核销**是当前主要风险点，需重点关注。")
            suggestions.append("")
            suggestions.append("**优化建议：**")
            suggestions.append("1. 建立预付费定期核销提醒机制")
            suggestions.append("2. 完善预付费管理流程")
        
        if not suggestions:
            suggestions.append("根据知识库搜索结果，建议加强风险管控机制。")
        
        suggestions.append("")
        suggestions.append("**知识来源：** 基于企业内部风险管理规范整理")
        
        return suggestions
    
    def _generate_processing_summary(self, tool_results: List[ToolResult]) -> str:
        """生成处理摘要"""
        total_steps = len(tool_results)
        successful_steps = sum(1 for r in tool_results if r.success)
        
        return f"执行了 {total_steps} 个步骤，成功 {successful_steps} 个"

    def _format_with_complex_prompt(self, tool_results: List[ToolResult],
                                   intent: IntentResult,
                                   complex_prompt) -> FormattedResponse:
        """
        🎯 使用复杂提示词指导的响应格式化

        这是关键方法：让响应格式能够根据复杂提示词的内容进行定制！

        Args:
            complex_prompt: ComplexPrompt对象
        """
        logger.info(f"🎯 [结果整合] 使用复杂提示词格式化: {complex_prompt.title}")

        # 提取复杂提示词的内容
        prompt_content = "\n".join(complex_prompt.processing_steps)
        prompt_query = complex_prompt.title

        # 获取省份名称
        province_name = self.province_map.get(intent.province_code, intent.province_code or "广西")

        # 构建响应内容
        content_parts = []

        # 🎯 根据复杂提示词定制标题
        if "风险" in prompt_query:
            title = f"# 📊 {province_name}网络电费风险分析报告"
        elif "成本" in prompt_query:
            title = f"# 💰 {province_name}网络电费成本分析报告"
        elif "预付费" in prompt_query:
            title = f"# 💳 {province_name}预付费管理分析报告"
        else:
            title = f"# 📋 {province_name}网络电费分析报告"

        content_parts.append(title)
        content_parts.append("")

        # 🎯 根据复杂提示词定制内容结构
        if "详细分析" in prompt_content or "深度分析" in prompt_content:
            # 详细分析模式
            content_parts.extend(self._build_detailed_analysis_section(tool_results, intent, prompt_content))
        elif "简要报告" in prompt_content or "概要" in prompt_content:
            # 简要报告模式
            content_parts.extend(self._build_summary_section(tool_results, intent, prompt_content))
        else:
            # 标准分析模式（保持原有逻辑）
            content_parts.extend(self._build_overview_section(tool_results))
            content_parts.extend(self._build_detail_section(tool_results))

        # 🎯 根据复杂提示词定制建议部分
        if "建议" in prompt_content or "优化" in prompt_content:
            suggestion_section = self._build_suggestion_section(tool_results, complex_prompt)
            content_parts.extend(suggestion_section)

        # 🎯 如果复杂提示词包含特定格式要求
        if "表格" in prompt_content:
            # 确保包含表格
            tables = self._extract_tables_from_results(tool_results)
            if tables:
                content_parts.append("\n## 📊 数据详情")
                content_parts.extend(tables)

        final_content = "\n".join(content_parts)

        return FormattedResponse(
            content=final_content,
            has_tables=self._contains_tables(final_content),
            response_type="complex_prompt_guided",
            metadata={
                "prompt_query": prompt_query,
                "prompt_content_length": len(prompt_content),
                "customized_format": True
            }
        )

    def _build_detailed_analysis_section(self, tool_results: List[ToolResult],
                                       intent: IntentResult,
                                       prompt_content: str) -> List[str]:
        """构建详细分析部分"""
        section = []

        section.append("## 🔍 详细分析")
        section.append("")

        # 分析每个工具的结果
        for i, result in enumerate(tool_results, 1):
            if result.success:
                section.append(f"### {i}. {result.tool_call.step_description if result.tool_call else '数据分析'}")

                if result.tool_call and result.tool_call.tool_name == "integrated_sql":
                    # SQL结果的详细分析
                    data = self._extract_data_from_sql_result(result)
                    if data:
                        section.append(f"查询到 {len(data)} 条记录，具体情况如下：")
                        section.append("")

                        # 生成表格
                        table = self._generate_table_from_data(data)
                        section.extend(table)
                        section.append("")

                        # 数据洞察
                        insights = self._generate_data_insights(data, prompt_content)
                        section.extend(insights)
                    else:
                        section.append("未查询到相关数据。")

                elif result.tool_call and result.tool_call.tool_name == "knowledge_search":
                    # 知识搜索结果的详细展示
                    knowledge_content = self._extract_knowledge_content(result)
                    if knowledge_content:
                        section.extend(knowledge_content)

                section.append("")

        return section

    def _build_summary_section(self, tool_results: List[ToolResult],
                             intent: IntentResult,
                             prompt_content: str) -> List[str]:
        """构建概要部分"""
        section = []

        section.append("## 📋 概要分析")
        section.append("")

        # 统计总体情况
        total_records = 0
        key_findings = []

        for result in tool_results:
            if result.success and result.tool_call and result.tool_call.tool_name == "integrated_sql":
                data = self._extract_data_from_sql_result(result)
                if data:
                    total_records += len(data)
                    # 提取关键发现
                    finding = self._extract_key_finding(data, result.tool_call.step_description)
                    if finding:
                        key_findings.append(finding)

        section.append(f"**总体情况：** 共分析 {total_records} 条数据记录")
        section.append("")

        if key_findings:
            section.append("**关键发现：**")
            for i, finding in enumerate(key_findings, 1):
                section.append(f"{i}. {finding}")
            section.append("")

        return section
