"""
结果整合器 - 专门负责格式化最终输出
"""

import re
import logging
from typing import Dict, Any, List, Optional
from dataclasses import dataclass

from .intent_analyzer import IntentResult, IntentType
from .tool_orchestrator import ToolResult

logger = logging.getLogger(__name__)


@dataclass
class FormattedResponse:
    """格式化的响应结果"""
    content: str
    has_tables: bool = False
    table_count: int = 0
    used_tools: List[str] = None
    processing_summary: str = ""


class ResponseFormatter:
    """
    结果整合器
    
    职责：
    1. 整合多个工具的输出结果
    2. 格式化最终答案
    3. 生成表格和可视化
    4. 应用业务规则和模板
    """
    
    def __init__(self):
        self.province_mapping = {
            "GX": "广西",
            "GZ": "广州", 
            "JT": "集团"
        }
    
    def format_final_response(self, tool_results: List[ToolResult], 
                            intent: IntentResult) -> FormattedResponse:
        """
        格式化最终响应
        
        Args:
            tool_results: 工具执行结果列表
            intent: 意图分析结果
            
        Returns:
            FormattedResponse: 格式化的响应
        """
        logger.info(f"📝 [结果整合] 开始格式化响应，工具结果数: {len(tool_results)}")
        
        if intent.intent_type == IntentType.COMPLEX_ANALYSIS:
            response = self._format_complex_analysis(tool_results, intent)
        elif intent.intent_type == IntentType.SIMPLE_QUERY:
            response = self._format_simple_query(tool_results, intent)
        else:
            response = self._format_default_response(tool_results, intent)
        
        # 添加处理摘要
        response.processing_summary = self._generate_processing_summary(tool_results)
        response.used_tools = [r.tool_call.tool_name for r in tool_results if r.tool_call]
        
        logger.info(f"✅ [结果整合] 响应格式化完成，包含表格: {response.has_tables}")
        return response
    
    def _format_complex_analysis(self, tool_results: List[ToolResult], 
                                intent: IntentResult) -> FormattedResponse:
        """格式化复杂分析结果"""
        
        # 获取省份名称
        province_name = self.province_mapping.get(intent.province_code, intent.province_code or "")
        
        # 构建响应内容
        content_parts = []
        
        # 标题
        content_parts.append(f"# 📊 {province_name}网络电费7月风险")
        content_parts.append("")
        
        # 总览部分
        overview_section = self._build_overview_section(tool_results)
        if overview_section:
            content_parts.extend(overview_section)
        
        # 明细部分
        detail_section = self._build_detail_section(tool_results)
        if detail_section:
            content_parts.extend(detail_section)
        
        # 建议部分
        suggestion_section = self._build_suggestion_section(tool_results)
        if suggestion_section:
            content_parts.extend(suggestion_section)
        
        content = "\n".join(content_parts)
        
        # 统计表格信息
        table_count = content.count("| --- |")
        has_tables = table_count > 0
        
        return FormattedResponse(
            content=content,
            has_tables=has_tables,
            table_count=table_count
        )
    
    def _format_simple_query(self, tool_results: List[ToolResult], 
                           intent: IntentResult) -> FormattedResponse:
        """格式化简单查询结果"""
        
        if not tool_results or not tool_results[0].success:
            return FormattedResponse(
                content="抱歉，查询失败。请稍后重试。",
                has_tables=False
            )
        
        result = tool_results[0].result
        
        # 如果结果包含表格，直接返回
        if isinstance(result, dict) and "summary" in result:
            content = result["summary"]
        else:
            content = str(result)
        
        # 统计表格信息
        table_count = content.count("| --- |")
        has_tables = table_count > 0
        
        return FormattedResponse(
            content=content,
            has_tables=has_tables,
            table_count=table_count
        )
    
    def _format_default_response(self, tool_results: List[ToolResult], 
                                intent: IntentResult) -> FormattedResponse:
        """默认响应格式化"""
        return self._format_simple_query(tool_results, intent)
    
    def _build_overview_section(self, tool_results: List[ToolResult]) -> List[str]:
        """构建总览部分"""
        # 查找第一个SQL查询结果（总体情况）
        overview_result = None
        for result in tool_results:
            if (result.success and result.tool_call and 
                result.tool_call.tool_name == "integrated_sql" and
                result.step_index == 0):
                overview_result = result
                break
        
        if not overview_result:
            return []
        
        section = []
        section.append("## 📋 费用风险分析总览：")
        section.append("")
        
        # 提取并格式化总览数据
        if isinstance(overview_result.result, dict):
            execution_result = overview_result.result.get("execution_result", {})
            data = execution_result.get("data", [])
            columns = execution_result.get("columns", [])
            
            if data and columns:
                # 生成总结文本
                total_records = len(data)
                section.append(f"2025年7月网络电费风险分析显示，共启用3项风险检查规则，发现{total_records}类风险问题。")
                section.append("")
                
                # 生成表格
                table_lines = self._generate_table(columns, data)
                section.extend(table_lines)
        
        return section
    
    def _build_detail_section(self, tool_results: List[ToolResult]) -> List[str]:
        """构建明细部分"""
        section = []
        section.append("## ⚠️ 费用风险分析明细：")
        section.append("")
        section.append("**重要说明：此部分展示各风险指标的详细查询结果**")
        section.append("")
        
        # 查找指标详情查询结果
        detail_results = [
            r for r in tool_results 
            if (r.success and r.tool_call and 
                r.tool_call.tool_name == "integrated_sql" and
                r.step_index > 0 and r.step_index < len(tool_results) - 1)
        ]
        
        for i, result in enumerate(detail_results):
            if isinstance(result.result, dict):
                execution_result = result.result.get("execution_result", {})
                data = execution_result.get("data", [])
                columns = execution_result.get("columns", [])
                
                if data and columns:
                    # 从工具调用参数中提取指标名称
                    indicator_name = self._extract_indicator_name(result.tool_call.parameters.get("question", ""))
                    
                    section.append(f"### {i+1}. {indicator_name}")
                    section.append("")
                    
                    # 生成明细表格
                    table_lines = self._generate_table(columns, data[:10])  # 限制显示行数
                    section.extend(table_lines)
                    
                    if len(data) > 10:
                        section.append(f"*注：共 {len(data)} 条记录，仅显示前 10 条*")
                        section.append("")
        
        return section
    
    def _build_suggestion_section(self, tool_results: List[ToolResult]) -> List[str]:
        """构建建议部分"""
        logger.info("🚀🚀🚀 [建议部分] 开始构建建议部分")
        section = []
        section.append("## 💡 费用风险管理优化建议：")
        section.append("")
        
        # 查找知识搜索结果
        knowledge_result = None
        logger.info(f"🔍 [建议部分] 开始查找知识搜索结果，共有 {len(tool_results)} 个工具结果")

        for i, result in enumerate(tool_results):
            logger.info(f"🔍 [建议部分] 工具结果 {i+1}: 工具名={result.tool_call.tool_name if result.tool_call else 'None'}, 成功={result.success}")
            logger.info(f"🔍 [建议部分] 工具结果 {i+1}: 结果类型={type(result.result)}, 结果预览={str(result.result)[:100]}...")

            if (result.success and result.tool_call and
                result.tool_call.tool_name == "knowledge_search"):
                knowledge_result = result
                logger.info(f"✅ [建议部分] 找到知识搜索结果 (工具结果 {i+1})")
                break

        if knowledge_result:
            logger.info(f"🔍 [建议部分] 知识搜索结果详细信息:")
            logger.info(f"   - 结果类型: {type(knowledge_result.result)}")
            logger.info(f"   - 结果长度: {len(str(knowledge_result.result))}")
            logger.info(f"   - 结果内容: {str(knowledge_result.result)[:500]}...")

            # 尝试多种方式解析知识搜索结果
            knowledge_text = None
            if isinstance(knowledge_result.result, str):
                knowledge_text = knowledge_result.result
                logger.info("✅ [建议部分] 知识搜索结果是字符串类型")
            elif isinstance(knowledge_result.result, dict):
                # 尝试从字典中提取文本
                if 'result' in knowledge_result.result:
                    knowledge_text = knowledge_result.result['result']
                    logger.info("✅ [建议部分] 从字典的'result'字段提取知识搜索结果")
                elif 'data' in knowledge_result.result:
                    knowledge_text = knowledge_result.result['data']
                    logger.info("✅ [建议部分] 从字典的'data'字段提取知识搜索结果")
                elif 'content' in knowledge_result.result:
                    knowledge_text = knowledge_result.result['content']
                    logger.info("✅ [建议部分] 从字典的'content'字段提取知识搜索结果")
                else:
                    # 直接传递整个字典，不要转换为字符串
                    knowledge_text = knowledge_result.result
                    logger.info("✅ [建议部分] 直接使用整个字典作为知识搜索结果")
            else:
                knowledge_text = str(knowledge_result.result)
                logger.info("⚠️ [建议部分] 非字符串类型，转换为字符串")

            if knowledge_text and len(knowledge_text.strip()) > 0:
                logger.info("✅ [建议部分] 使用知识搜索结果生成建议")
                logger.info(f"🔍 [建议部分] 传递给解析器的知识文本长度: {len(knowledge_text)}")
                logger.info(f"🔍 [建议部分] 传递给解析器的知识文本类型: {type(knowledge_text)}")
                # 解析知识搜索结果
                suggestions = self._parse_knowledge_result(knowledge_text)
                section.extend(suggestions)
            else:
                logger.warning("⚠️ [建议部分] 知识搜索结果为空，使用默认建议")
                self._add_default_suggestions(section)
        else:
            logger.info("⚠️ [建议部分] 未找到知识搜索结果，使用默认建议")
            self._add_default_suggestions(section)
        
        return section

    def _add_default_suggestions(self, section: List[str]) -> None:
        """添加默认建议"""
        section.append("**预付费超期未核销**是当前主要风险点，需重点关注。")
        section.append("")
        section.append("**优化建议：**")
        section.append("1. 建立预付费定期核销提醒机制")
        section.append("2. 完善发票管理流程，确保发票规范性")
        section.append("3. 加强合同单价管控，避免超标缴费")
        section.append("4. 定期开展风险排查和整改工作")
    
    def _generate_table(self, columns: List[str], data: List[Dict]) -> List[str]:
        """生成标准markdown表格"""
        if not columns or not data:
            return ["查询结果为空。", ""]
        
        table_lines = []
        
        # 清理列名
        clean_columns = [str(col).strip().replace('|', '').replace('\n', '') for col in columns]
        
        # 表头
        header = "| " + " | ".join(clean_columns) + " |"
        separator = "| " + " | ".join(["---"] * len(clean_columns)) + " |"
        
        table_lines.append(header)
        table_lines.append(separator)
        
        # 数据行
        for row in data:
            row_values = []
            for col in columns:
                value = row.get(col, "")
                # 清理值
                clean_value = str(value).replace('|', '｜').replace('\n', ' ').strip()
                if len(clean_value) > 30:
                    clean_value = clean_value[:27] + "..."
                row_values.append(clean_value)
            
            row_line = "| " + " | ".join(row_values) + " |"
            table_lines.append(row_line)
        
        table_lines.append("")  # 空行
        return table_lines
    
    def _extract_indicator_name(self, question: str) -> str:
        """从查询问题中提取指标名称"""
        # 简单的关键词匹配
        indicators = {
            "预付费": "预付费超期未核销",
            "转供电": "转供电非正规发票", 
            "缴费单": "已报账缴费单超过合同约定单价",
            "发票": "转供电非正规发票"
        }
        
        for keyword, indicator in indicators.items():
            if keyword in question:
                return indicator
        
        return "风险指标"
    
    def _parse_knowledge_result(self, knowledge_text: str) -> List[str]:
        """解析知识搜索结果并生成建议"""
        suggestions = []

        try:
            # 尝试解析JSON格式的知识搜索结果
            import json
            logger.info(f"🔍 [建议解析] 开始解析知识搜索结果，输入类型: {type(knowledge_text)}")

            if isinstance(knowledge_text, str):
                try:
                    knowledge_data = json.loads(knowledge_text)
                    logger.info("✅ [建议解析] 成功解析JSON字符串")
                except json.JSONDecodeError as e:
                    logger.warning(f"⚠️ [建议解析] JSON解析失败: {e}")
                    # 如果不是JSON，直接使用字符串内容
                    knowledge_data = {"content": knowledge_text}
            elif isinstance(knowledge_text, dict):
                knowledge_data = knowledge_text
                logger.info("✅ [建议解析] 输入已经是字典类型")
                logger.info(f"🔍 [建议解析] 字典包含的键: {list(knowledge_data.keys())}")
            else:
                logger.warning(f"⚠️ [建议解析] 未知的输入类型: {type(knowledge_text)}")
                knowledge_data = {"content": str(knowledge_text)}

            logger.info(f"🔍 [建议解析] 知识数据结构: {list(knowledge_data.keys())}")

            # 提取知识库结果
            results = knowledge_data.get('results', [])
            logger.info(f"📚 [建议解析] 解析到 {len(results)} 条知识库结果")

            # 分析知识库内容并生成针对性建议
            risk_keywords = {
                '预付费': [],
                '转供电': [],
                '单价': [],
                '稽核': [],
                '管控': []
            }

            # 提取关键信息
            all_content = ""
            for result in results:
                content = result.get('content', '')
                title = result.get('title', '')
                all_content += f"{title}: {content} "

                # 分类关键词
                if '预付费' in content:
                    risk_keywords['预付费'].append(content[:100])
                if '转供电' in content:
                    risk_keywords['转供电'].append(content[:100])
                if '单价' in content or '电价' in content:
                    risk_keywords['单价'].append(content[:100])
                if '稽核' in content:
                    risk_keywords['稽核'].append(content[:100])
                if '管控' in content:
                    risk_keywords['管控'].append(content[:100])

            logger.info(f"📊 [建议解析] 关键词统计: {[(k, len(v)) for k, v in risk_keywords.items()]}")

            # 生成针对性建议
            if risk_keywords['预付费']:
                suggestions.append("**预付费超期未核销**是当前主要风险点，需重点关注。")
                suggestions.append("")
                suggestions.append("**优化建议：**")
                suggestions.append("1. 建立预付费定期核销提醒机制")
                suggestions.append("2. 完善预付费管理流程，确保及时核销")

            if risk_keywords['转供电']:
                if not any("优化建议" in s for s in suggestions):
                    suggestions.append("**优化建议：**")
                suggestions.append("3. 加强转供电发票规范性管理")
                suggestions.append("4. 建立发票审核机制，确保发票合规")

            if risk_keywords['单价'] or risk_keywords['管控']:
                if not any("优化建议" in s for s in suggestions):
                    suggestions.append("**优化建议：**")
                suggestions.append("5. 加强合同单价管控，避免超标缴费")
                suggestions.append("6. 定期开展合同执行情况检查")

            if risk_keywords['稽核']:
                if not any("优化建议" in s for s in suggestions):
                    suggestions.append("**优化建议：**")
                suggestions.append("7. 建立网络电费集中稽核机制")
                suggestions.append("8. 依托智能电表等数据进行多维度分析")

            # 如果没有生成具体建议，使用通用建议
            if len(suggestions) <= 2:
                suggestions.append("根据知识库搜索结果，建议：")
                suggestions.append("1. 加强费用风险管控机制")
                suggestions.append("2. 完善内部审核流程")
                suggestions.append("3. 定期开展风险排查和整改工作")

            suggestions.append("")
            suggestions.append("**知识来源：** 基于企业内部风险管理规范和知识库整理")

        except Exception as e:
            logger.warning(f"⚠️ [建议解析] 解析知识搜索结果失败: {e}")
            # 回退到简单建议
            suggestions = [
                "根据知识库搜索结果，建议加强风险管控机制。",
                "",
                "**知识来源：** 基于企业内部风险管理规范整理"
            ]

        return suggestions
    
    def _generate_processing_summary(self, tool_results: List[ToolResult]) -> str:
        """生成处理摘要"""
        total_steps = len(tool_results)
        successful_steps = sum(1 for r in tool_results if r.success)
        
        return f"执行了 {total_steps} 个步骤，成功 {successful_steps} 个"
