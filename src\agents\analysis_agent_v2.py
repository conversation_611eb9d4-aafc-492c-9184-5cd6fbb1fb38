"""
分析Agent V2 - 基于组件化架构的新实现
保持API完全兼容，内部使用新的三个组件
"""

import time
import logging
from typing import Dict, Any, List, Optional

from .components.intent_analyzer import IntentAnalyzer, IntentResult
from .components.tool_orchestrator import ToolOrchestrator, ExecutionPlan
from .components.response_formatter import ResponseFormatter, FormattedResponse
from .vector_enhanced_agent import VectorEnhancedAgent

logger = logging.getLogger(__name__)


class AnalysisAgentV2(VectorEnhancedAgent):
    """
    分析Agent V2
    
    基于组件化架构重新实现，保持API完全兼容
    
    架构：
    1. IntentAnalyzer - 意图理解
    2. ToolOrchestrator - 工具编排  
    3. ResponseFormatter - 结果整合
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # 初始化新组件
        self.intent_analyzer = IntentAnalyzer()
        self.tool_orchestrator = ToolOrchestrator(self.tools)
        self.response_formatter = ResponseFormatter()
        
        # 控制开关
        self.use_v2_architecture = True  # 可以通过配置控制是否启用新架构
        
        logger.info("🏗️ [AnalysisAgentV2] 组件化架构初始化完成")
    
    def process_message(self, message: str, conversation_history: Optional[List[Dict]] = None, 
                       system_message: str = None) -> Dict[str, Any]:
        """
        处理用户消息 - 同步版本
        
        保持API完全兼容，内部使用新架构
        """
        if not self.use_v2_architecture:
            # 回退到原有实现
            return super().process_message(message, conversation_history, system_message)
        
        logger.info(f"🤖 [AnalysisAgentV2] 开始处理用户消息: {message}")
        start_time = time.time()
        
        try:
            # 第一步：意图理解
            intent_start = time.time()
            
            # 先进行向量搜索（保持兼容性）
            user_question = self._extract_user_question(message)
            matched_prompts = self._search_vector_database(user_question)
            
            # 分析意图
            intent = self.intent_analyzer.analyze_intent(
                user_input=message,
                system_message=system_message,
                vector_prompts=matched_prompts
            )
            
            intent_duration = time.time() - intent_start
            logger.info(f"🎯 [AnalysisAgentV2] 意图分析完成 (用时: {intent_duration:.2f}秒)")
            
            # 第二步：工具编排和执行
            orchestration_start = time.time()
            
            # 🎯 检查是否需要使用复杂提示词的LLM驱动模式
            if matched_prompts and len(matched_prompts) > 0:
                # matched_prompts是(ComplexPrompt, similarity)的元组列表
                complex_prompt, similarity = matched_prompts[0]
                prompt_content = "\n".join(complex_prompt.processing_steps)

                # 如果复杂提示词包含多步骤逻辑，使用LLM驱动模式
                if self._requires_llm_driven_execution(prompt_content):
                    logger.info("🧠 [AnalysisAgentV2] 复杂提示词需要LLM驱动执行，回退到增强模式")
                    return self._execute_with_complex_prompt(message, conversation_history, system_message, complex_prompt)

            # 使用V2组件化执行
            execution_plan = self.tool_orchestrator.plan_execution(intent, matched_prompts)
            tool_results = self.tool_orchestrator.execute_plan(execution_plan, intent, matched_prompts)
            
            orchestration_duration = time.time() - orchestration_start
            logger.info(f"🔧 [AnalysisAgentV2] 工具编排完成 (用时: {orchestration_duration:.2f}秒)")
            
            # 第三步：结果整合
            formatting_start = time.time()
            
            # 传递复杂提示词信息到响应格式化器
            formatted_response = self.response_formatter.format_final_response(tool_results, intent, matched_prompts)
            
            formatting_duration = time.time() - formatting_start
            logger.info(f"📝 [AnalysisAgentV2] 结果整合完成 (用时: {formatting_duration:.2f}秒)")
            
            # 构建兼容的返回结果
            total_duration = time.time() - start_time
            
            result = {
                "response": formatted_response.content,
                "tool_used": len(tool_results) > 0,
                "tool_calls_history": self._build_compatible_tool_history(tool_results),
                "iterations": len(tool_results),
                "timeout": False,
                "final_response": formatted_response.content,
                "timing": {
                    "total": total_duration,
                    "intent_analysis": intent_duration,
                    "tool_orchestration": orchestration_duration,
                    "response_formatting": formatting_duration
                },
                # V2 特有信息
                "v2_info": {
                    "intent_type": intent.intent_type.value,
                    "confidence": intent.confidence,
                    "multi_step": intent.requires_multi_step,
                    "steps_executed": len(tool_results),
                    "has_tables": formatted_response.has_tables,
                    "processing_summary": formatted_response.processing_summary
                },
                # 保持向量增强的兼容性
                "used_complex_prompt": len(matched_prompts) > 0,
                "complex_prompt_info": {
                    "similarity": matched_prompts[0][1] if matched_prompts else 0,
                    "title": matched_prompts[0][0].title if matched_prompts else ""
                } if matched_prompts else None
            }
            
            logger.info(f"✅ [AnalysisAgentV2] 处理完成 (总用时: {total_duration:.2f}秒)")
            return result
            
        except Exception as e:
            logger.error(f"💥 [AnalysisAgentV2] 处理失败: {str(e)}")
            
            # 错误时回退到原有实现
            logger.info("🔄 [AnalysisAgentV2] 回退到原有实现")
            return super().process_message(message, conversation_history, system_message)
    
    async def process_message_async(self, message: str, conversation_history: Optional[List[Dict]] = None, 
                                  real_time_callback: Optional[callable] = None,
                                  system_message: str = None) -> Dict[str, Any]:
        """
        异步处理用户消息
        
        保持API完全兼容，内部使用新架构
        """
        if not self.use_v2_architecture:
            # 回退到原有实现
            return await super().process_message_async(message, conversation_history, real_time_callback, system_message)
        
        logger.info(f"🤖 [AnalysisAgentV2] 开始异步处理用户消息: {message}")
        
        # 实时回调支持 - 修复参数错误
        if real_time_callback:
            await real_time_callback("agent_progress", {"message": "🔍 开始分析用户意图..."})

        try:
            # 第一步：意图理解
            user_question = self._extract_user_question(message)
            matched_prompts = self._search_vector_database(user_question)

            intent = self.intent_analyzer.analyze_intent(
                user_input=message,
                system_message=system_message,
                vector_prompts=matched_prompts
            )

            if real_time_callback:
                await real_time_callback("agent_progress", {
                    "message": f"🎯 意图分析完成: {intent.intent_type.value}",
                    "intent_type": intent.intent_type.value,
                    "confidence": intent.confidence
                })

            # 第二步：工具编排和执行
            execution_plan = self.tool_orchestrator.plan_execution(intent)

            if real_time_callback:
                await real_time_callback("agent_progress", {
                    "message": f"📋 执行计划: {execution_plan.total_steps} 个步骤",
                    "total_steps": execution_plan.total_steps
                })

            # 执行计划（带实时反馈）
            tool_results = []
            for i, tool_call in enumerate(execution_plan.tool_calls):
                if real_time_callback:
                    await real_time_callback("agent_progress", {
                        "message": f"🔧 执行步骤 {i+1}/{execution_plan.total_steps}: {tool_call.step_description}",
                        "current_step": i+1,
                        "total_steps": execution_plan.total_steps,
                        "step_description": tool_call.step_description
                    })

                # 这里简化处理，实际应该异步执行
                result = self.tool_orchestrator._execute_tool_with_retry(tool_call, i)
                result.step_index = i
                result.tool_call = tool_call
                tool_results.append(result)

                if real_time_callback and result.success:
                    await real_time_callback("agent_progress", {
                        "message": f"✅ 步骤 {i+1} 完成",
                        "step_completed": i+1
                    })

            # 第三步：结果整合
            if real_time_callback:
                await real_time_callback("agent_progress", {"message": "📝 正在整合结果..."})

            formatted_response = self.response_formatter.format_final_response(tool_results, intent)

            if real_time_callback:
                await real_time_callback("agent_progress", {"message": "✅ 分析完成"})
            
            # 构建兼容的返回结果
            result = {
                "response": formatted_response.content,
                "tool_used": len(tool_results) > 0,
                "tool_calls_history": self._build_compatible_tool_history(tool_results),
                "iterations": len(tool_results),
                "timeout": False,
                "final_response": formatted_response.content,
                "v2_info": {
                    "intent_type": intent.intent_type.value,
                    "confidence": intent.confidence,
                    "multi_step": intent.requires_multi_step,
                    "steps_executed": len(tool_results),
                    "has_tables": formatted_response.has_tables
                },
                "used_complex_prompt": len(matched_prompts) > 0
            }
            
            return result
            
        except Exception as e:
            logger.error(f"💥 [AnalysisAgentV2] 异步处理失败: {str(e)}")
            
            if real_time_callback:
                await real_time_callback("agent_error", {
                    "message": "⚠️ 处理出错，回退到备用方案...",
                    "error": str(e)
                })

            # 错误时回退到原有实现
            return await super().process_message_async(message, conversation_history, real_time_callback, system_message)
    
    def _build_compatible_tool_history(self, tool_results: List) -> List[Dict]:
        """构建兼容的工具调用历史"""
        history = []

        for i, result in enumerate(tool_results):
            if result.tool_call:
                # 🔧 构建完全兼容原有格式的工具调用历史
                tool_duration = result.timing.get("duration", 0) if result.timing else 0

                history.append({
                    "iteration": i + 1,
                    "tool_call": {
                        "tool_name": result.tool_call.tool_name,
                        "parameters": result.tool_call.parameters
                    },
                    "tool_result": {
                        "success": result.success,
                        "result": result.result,
                        "error": result.error
                    },
                    "timing": {
                        "tool_duration": tool_duration,  # 🔧 前端需要的字段
                        "duration": tool_duration,
                        "llm_duration": 0,  # V2架构中LLM时间单独计算
                        "total_duration": tool_duration
                    },
                    # 🔧 添加原有格式需要的字段
                    "llm_response": f"调用工具: {result.tool_call.tool_name}",
                    "tool_execution_time": tool_duration,
                    "step_description": result.tool_call.step_description
                })

        return history
    
    def toggle_v2_architecture(self, enabled: bool):
        """切换V2架构开关"""
        self.use_v2_architecture = enabled
        logger.info(f"🔧 [AnalysisAgentV2] V2架构 {'启用' if enabled else '禁用'}")
    
    def get_architecture_info(self) -> Dict[str, Any]:
        """获取架构信息"""
        return {
            "version": "v2",
            "architecture": "component_based",
            "components": [
                "IntentAnalyzer",
                "ToolOrchestrator", 
                "ResponseFormatter"
            ],
            "enabled": self.use_v2_architecture,
            "compatible_with": "VectorEnhancedAgent"
        }

    def _requires_llm_driven_execution(self, prompt_content: str) -> bool:
        """
        判断复杂提示词是否需要LLM驱动执行

        如果提示词包含复杂的多步骤逻辑，需要LLM来动态判断和执行
        """
        # 检查是否包含需要动态判断的关键词
        llm_driven_keywords = [
            "从步骤",
            "提取",
            "每个指标",
            "金额最大",
            "仔细确保",
            "然后再进行",
            "必须为",
            "都执行一次"
        ]

        return any(keyword in prompt_content for keyword in llm_driven_keywords)

    def _execute_with_complex_prompt(self, message: str, conversation_history: List,
                                   system_message: str, complex_prompt) -> Dict[str, Any]:
        """
        使用复杂提示词进行LLM驱动执行

        这种模式下，让LLM根据复杂提示词的内容进行动态推理和工具调用

        Args:
            complex_prompt: ComplexPrompt对象
        """
        logger.info("🧠 [AnalysisAgentV2] 开始LLM驱动的复杂提示词执行")

        # 🔧 关键修复：先提取省份代码，确保工具调用时使用正确的数据库
        province_code = self._extract_province_code_from_system_message(system_message)
        if province_code:
            logger.info(f"🎯 [AnalysisAgentV2] LLM驱动模式提取到省份代码: {province_code}")
            # 设置当前系统消息，供工具调用时使用
            self.current_system_message = system_message

        # 构建增强的系统消息，包含复杂提示词的内容
        prompt_content = "\n".join(complex_prompt.processing_steps)
        enhanced_system_message = system_message + "\n\n" + prompt_content

        # 使用父类的增强执行模式
        return super().process_message(message, conversation_history, enhanced_system_message)

    def _extract_province_code_from_system_message(self, system_message: str) -> Optional[str]:
        """从系统消息中提取省份代码"""
        if not system_message:
            return None

        # 查找"数据库:省份代码"的模式
        import re
        pattern = r'数据库\s*[:：]\s*([A-Z]{1,3})'
        match = re.search(pattern, system_message)
        if match:
            return match.group(1).upper()

        return None
