#!/usr/bin/env python3
"""
测试步骤优化功能
"""

import requests
import json
import time

def test_step_optimization():
    """测试步骤优化功能"""
    
    print("🧪 测试步骤优化功能")
    print("=" * 60)
    
    # 构建请求
    request_data = {
        "model": "sql-agent",
        "messages": [
            {
                "role": "system",
                "content": "数据库:GX"
            },
            {
                "role": "user",
                "content": "网络电费7月存在哪些风险"
            }
        ],
        "stream": False
    }
    
    try:
        print("📤 发送请求...")
        start_time = time.time()
        
        response = requests.post(
            "http://localhost:8001/v1/chat/completions",
            json=request_data,
            headers={"Content-Type": "application/json"},
            timeout=60
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        if response.status_code == 200:
            print(f"✅ 请求成功! (用时: {duration:.2f}秒)")
            
            result = response.json()
            content = result.get("choices", [{}])[0].get("message", {}).get("content", "")
            
            print(f"\n📋 响应内容:")
            print("-" * 60)
            print(content)
            print("-" * 60)
            
            # 检查是否包含知识搜索的结果
            knowledge_indicators = [
                "知识来源",
                "知识库",
                "改进建议",
                "优化建议",
                "预付费超期未核销",
                "转供电非正规发票"
            ]
            
            found_indicators = [indicator for indicator in knowledge_indicators if indicator in content]
            
            print(f"\n📊 知识搜索指标检查:")
            for indicator in knowledge_indicators:
                status = "✅" if indicator in content else "❌"
                print(f"   {status} {indicator}")
            
            if found_indicators:
                print(f"\n✅ 成功！找到 {len(found_indicators)} 个知识搜索相关指标")
                print(f"   找到的指标: {found_indicators}")
            else:
                print(f"\n⚠️ 没有找到知识搜索相关指标，可能没有调用知识搜索")
            
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")

if __name__ == "__main__":
    test_step_optimization()
