#!/usr/bin/env python3
"""
测试改进的V2架构 - 支持复杂提示词
"""

import requests
import json
import time

def test_v2_with_complex_prompts():
    """测试V2架构使用复杂提示词的能力"""
    
    # API端点
    url = "http://localhost:8001/v1/chat/completions"
    
    # 测试用例
    test_cases = [
        {
            "name": "匹配复杂提示词的查询",
            "content": "网络电费7月存在哪些风险",
            "expected": "应该使用复杂提示词的内容指导执行",
            "description": "这个查询会匹配到复杂提示词，观察是否使用了提示词的具体内容"
        },
        {
            "name": "不匹配复杂提示词的查询", 
            "content": "2025年4月电费总额是多少",
            "expected": "应该使用V2默认逻辑",
            "description": "这个查询不会匹配复杂提示词，应该走默认逻辑"
        },
        {
            "name": "再次测试复杂提示词（验证修改效果）",
            "content": "网络电费7月存在哪些风险", 
            "expected": "如果修改了复杂提示词，响应应该有变化",
            "description": "重复第一个查询，用于验证复杂提示词修改后的效果"
        }
    ]
    
    headers = {"Content-Type": "application/json"}
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{'='*70}")
        print(f"🧪 测试用例 {i}: {test_case['name']}")
        print(f"📤 查询内容: {test_case['content']}")
        print(f"🎯 预期行为: {test_case['expected']}")
        print(f"📝 说明: {test_case['description']}")
        print("="*70)
        
        data = {
            "model": "analysis-agent",
            "messages": [
                {
                    "role": "system",
                    "content": "数据库:GZ\n\n用户问题: " + test_case["content"]
                },
                {
                    "role": "user",
                    "content": test_case["content"]
                }
            ],
            "temperature": 0.1,
            "max_tokens": 4000,
            "stream": False
        }
        
        try:
            print("📤 发送请求...")
            start_time = time.time()
            response = requests.post(url, json=data, headers=headers, timeout=120)
            end_time = time.time()
            
            if response.status_code == 200:
                result = response.json()
                content = result.get("choices", [{}])[0].get("message", {}).get("content", "")
                
                print(f"✅ 请求成功! (用时: {end_time - start_time:.2f}秒)")
                print("\n📋 响应内容:")
                print("-" * 60)
                print(content)
                print("-" * 60)
                
                # 详细分析响应特征
                print("\n🔍 响应特征分析:")
                
                # 检查工具调用情况
                if "步骤1:" in content and "步骤2:" in content:
                    step_count = content.count("步骤")
                    print(f"🔧 检测到多步骤执行: {step_count} 个步骤")
                elif "步骤1:" in content:
                    print("🔧 检测到单步骤执行")
                else:
                    print("🔧 未检测到明确的步骤信息")
                
                # 检查响应格式
                if "# 📊" in content:
                    print("📊 使用了风险分析报告格式")
                elif "# 💰" in content:
                    print("💰 使用了成本分析报告格式")
                elif "# 💳" in content:
                    print("💳 使用了预付费分析报告格式")
                elif "# 📋" in content:
                    print("📋 使用了通用分析报告格式")
                else:
                    print("📄 使用了简单响应格式")
                
                # 检查表格情况
                table_count = content.count("| --- |")
                if table_count > 0:
                    print(f"📊 包含 {table_count} 个数据表格")
                else:
                    print("📊 未包含数据表格")
                
                # 检查建议部分
                if "优化建议" in content or "管理建议" in content:
                    print("💡 包含优化建议部分")
                else:
                    print("💡 未包含建议部分")
                
                # 检查知识库使用
                if "基于知识库搜索" in content:
                    print("📚 使用了知识库搜索结果")
                elif "知识来源" in content:
                    print("📚 包含知识来源信息")
                else:
                    print("📚 未明确使用知识库")
                
            else:
                print(f"❌ 请求失败: {response.status_code}")
                print(f"错误信息: {response.text}")
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
        
        # 在测试用例之间稍作停顿
        if i < len(test_cases):
            print("\n⏳ 等待3秒后继续下一个测试...")
            time.sleep(3)
    
    print(f"\n🎉 所有测试完成!")

def test_prompt_modification_guide():
    """提供复杂提示词修改指南"""
    print("\n" + "="*70)
    print("🔧 复杂提示词修改测试指南")
    print("="*70)
    
    print("""
📝 测试说明：
现在V2架构会优先使用复杂提示词的内容来指导执行和格式化。

🎯 测试步骤：
1. 在复杂提示词管理系统中找到 "网络电费7月存在哪些风险" 这个提示词
2. 修改其内容，添加特定指令
3. 重新运行此测试脚本
4. 观察响应的变化

💡 可以添加的指令示例：

🔧 工具调用指令：
- "SQL查询: 2025年7月广西网络电费成本详细分析"
- "SQL查询: 预付费管理情况统计"
- "知识搜索: 电费成本优化策略"
- "知识搜索: 预付费风险管控措施"

📊 格式控制指令：
- "详细分析" - 生成详细的多步骤分析报告
- "简要报告" - 生成简洁的概要报告
- "表格" - 确保响应包含数据表格
- "成本分析" - 标题会变为成本分析报告
- "预付费分析" - 标题会变为预付费分析报告

📋 完整示例：
在复杂提示词内容中添加：
```
详细分析
SQL查询: 2025年7月网络电费成本构成分析
SQL查询: 预付费超期未核销详细情况
知识搜索: 电费成本控制最佳实践
表格
成本分析
```

🔍 预期效果：
- 执行3个工具调用（2个SQL + 1个知识搜索）
- 生成详细分析格式的响应
- 标题变为 "💰 广西网络电费成本分析报告"
- 确保包含数据表格
- 包含基于知识库的优化建议

⚠️ 注意事项：
- 修改后需要重新发送查询才能看到效果
- 可以通过服务器日志确认是否使用了复杂提示词
- 建议先备份原有的复杂提示词内容
""")

def show_log_analysis_tips():
    """显示日志分析提示"""
    print("\n" + "="*70)
    print("📊 服务器日志分析提示")
    print("="*70)
    
    print("""
🔍 关键日志标识：

✅ 使用复杂提示词的标志：
- "🎯 [工具编排] 发现 X 个匹配的复杂提示词，使用提示词指导执行"
- "🎯 [工具编排] 使用复杂提示词规划: 网络电费7月存在哪些风险"
- "🎯 [结果整合] 使用复杂提示词指导响应格式化"

🔄 使用默认逻辑的标志：
- "📋 [工具编排] 开始规划执行计划，意图类型: COMPLEX_ANALYSIS"
- 没有出现 "🎯" 标识的日志

📚 工具调用日志：
- "✅ SQL查询完成，返回 X 条记录"
- "✅ 知识库查询成功，找到 X 条相关信息"

🎯 通过这些日志可以确认：
1. 是否匹配到了复杂提示词
2. 是否使用了提示词的内容
3. 执行了哪些工具调用
4. 每个步骤的执行结果
""")

if __name__ == "__main__":
    print("🤖 V2架构复杂提示词支持测试")
    print("🎯 用于验证复杂提示词修改是否生效")
    
    # 测试基本功能
    test_v2_with_complex_prompts()
    
    # 显示修改指南
    test_prompt_modification_guide()
    
    # 显示日志分析提示
    show_log_analysis_tips()
