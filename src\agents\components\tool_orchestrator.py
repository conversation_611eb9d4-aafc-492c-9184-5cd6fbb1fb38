"""
工具编排器 - 专门负责规划和执行工具调用序列
"""

import time
import logging
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from .intent_analyzer import IntentResult, IntentType

logger = logging.getLogger(__name__)


@dataclass
class ToolCall:
    """工具调用定义"""
    tool_name: str
    parameters: Dict[str, Any]
    step_description: str
    depends_on: List[int] = None  # 依赖的步骤索引
    

@dataclass
class ToolResult:
    """工具执行结果"""
    success: bool
    result: Any
    error: Optional[str] = None
    timing: Dict[str, float] = None
    step_index: int = 0
    tool_call: ToolCall = None


@dataclass
class ExecutionPlan:
    """执行计划"""
    tool_calls: List[ToolCall]
    total_steps: int
    estimated_duration: float
    requires_sequential: bool = True


class ToolOrchestrator:
    """
    工具编排器
    
    职责：
    1. 根据意图结果规划工具调用序列
    2. 管理工具调用的执行顺序
    3. 处理工具间的数据传递
    4. 错误恢复和重试机制
    """
    
    def __init__(self, tools: List):
        self.tools = tools
        self.tool_map = {tool.name: tool for tool in tools}
        self.max_retries = 2
        
    def plan_execution(self, intent: IntentResult, matched_prompts: List = None) -> ExecutionPlan:
        """
        规划执行计划

        Args:
            intent: 意图分析结果
            matched_prompts: 匹配的复杂提示词列表

        Returns:
            ExecutionPlan: 执行计划
        """
        logger.info(f"📋 [工具编排] 开始规划执行计划，意图类型: {intent.intent_type.value}")

        # 🎯 优先使用复杂提示词的执行计划
        if matched_prompts and len(matched_prompts) > 0:
            logger.info(f"🎯 [工具编排] 发现 {len(matched_prompts)} 个匹配的复杂提示词，使用提示词指导执行")
            # matched_prompts是(ComplexPrompt, similarity)的元组列表
            complex_prompt, similarity = matched_prompts[0]
            plan = self._plan_from_complex_prompt(intent, complex_prompt)  # 使用第一个匹配的提示词
        elif intent.intent_type == IntentType.COMPLEX_ANALYSIS and intent.requires_multi_step:
            plan = self._plan_complex_analysis(intent)
        elif intent.intent_type == IntentType.SIMPLE_QUERY:
            plan = self._plan_simple_query(intent)
        else:
            plan = self._plan_default_execution(intent)
            
        logger.info(f"✅ [工具编排] 执行计划完成，共 {plan.total_steps} 个步骤")
        return plan
    
    def execute_plan(self, plan: ExecutionPlan, intent: IntentResult, matched_prompts: List = None) -> List[ToolResult]:
        """
        执行计划

        Args:
            plan: 执行计划
            intent: 意图分析结果
            matched_prompts: 匹配的复杂提示词列表
            intent: 意图结果
            
        Returns:
            List[ToolResult]: 执行结果列表
        """
        logger.info(f"🚀 [工具编排] 开始执行计划，共 {plan.total_steps} 个步骤")
        
        results = []
        execution_context = {
            "intent": intent,
            "previous_results": [],
            "extracted_data": {}
        }
        
        for i, tool_call in enumerate(plan.tool_calls):
            logger.info(f"🔧 [工具编排] 执行步骤 {i+1}/{plan.total_steps}: {tool_call.step_description}")
            
            # 检查依赖
            if tool_call.depends_on:
                if not self._check_dependencies(tool_call.depends_on, results):
                    logger.error(f"❌ [工具编排] 步骤 {i+1} 依赖检查失败")
                    continue
            
            # 动态调整参数
            adjusted_call = self._adjust_parameters(tool_call, execution_context)
            
            # 执行工具
            result = self._execute_tool_with_retry(adjusted_call, i)
            result.step_index = i
            result.tool_call = adjusted_call
            
            results.append(result)
            execution_context["previous_results"] = results
            
            # 更新执行上下文
            if result.success:
                self._update_execution_context(result, execution_context)
                logger.info(f"✅ [工具编排] 步骤 {i+1} 执行成功")
            else:
                logger.error(f"❌ [工具编排] 步骤 {i+1} 执行失败: {result.error}")
                
                # 根据错误类型决定是否继续
                if not self._should_continue_on_error(result, plan):
                    logger.error(f"🛑 [工具编排] 关键步骤失败，停止执行")
                    break
        
        logger.info(f"🏁 [工具编排] 执行完成，成功 {sum(1 for r in results if r.success)}/{len(results)} 个步骤")
        return results
    
    def _plan_complex_analysis(self, intent: IntentResult) -> ExecutionPlan:
        """规划复杂分析的执行计划"""
        tool_calls = []
        
        # 步骤1：查询总体情况
        tool_calls.append(ToolCall(
            tool_name="integrated_sql",
            parameters={
                "question": f"{intent.time_range or '2025年7月'}网络电费风险情况",
                "target_database": intent.province_code or "GX"
            },
            step_description="查询总体风险情况"
        ))
        
        # 步骤2：查询第一个重要指标的详情（简化处理）
        tool_calls.append(ToolCall(
            tool_name="integrated_sql",
            parameters={
                "question": f"{intent.time_range or '2025年7月'}风险指标预付费超期未核销情况",
                "target_database": intent.province_code or "GX"
            },
            step_description="查询预付费超期未核销详情",
            depends_on=[0]  # 依赖第一步的结果
        ))
        
        # 步骤3：查询第二个重要指标详情
        tool_calls.append(ToolCall(
            tool_name="integrated_sql",
            parameters={
                "question": f"{intent.time_range or '2025年7月'}风险指标转供电非正规发票情况",
                "target_database": intent.province_code or "GX"
            },
            step_description="查询转供电非正规发票详情",
            depends_on=[0]
        ))

        # 步骤4：查询第三个重要指标详情
        tool_calls.append(ToolCall(
            tool_name="integrated_sql",
            parameters={
                "question": f"{intent.time_range or '2025年7月'}风险指标已报账缴费单超过合同约定单价情况",
                "target_database": intent.province_code or "GX"
            },
            step_description="查询已报账缴费单超过合同约定单价详情",
            depends_on=[0]
        ))

        # 步骤5：搜索改进建议
        tool_calls.append(ToolCall(
            tool_name="knowledge_search",
            parameters={
                "query": "网络电费风险管理 改进建议"
            },
            step_description="搜索风险管理改进建议"
        ))
        
        return ExecutionPlan(
            tool_calls=tool_calls,
            total_steps=len(tool_calls),
            estimated_duration=30.0,
            requires_sequential=True
        )
    
    def _plan_simple_query(self, intent: IntentResult) -> ExecutionPlan:
        """规划简单查询的执行计划"""
        tool_calls = [
            ToolCall(
                tool_name="integrated_sql",
                parameters={
                    "question": intent.user_question,
                    "target_database": intent.province_code or "GX"
                },
                step_description=f"查询: {intent.user_question}"
            )
        ]
        
        return ExecutionPlan(
            tool_calls=tool_calls,
            total_steps=1,
            estimated_duration=5.0,
            requires_sequential=False
        )
    
    def _plan_default_execution(self, intent: IntentResult) -> ExecutionPlan:
        """默认执行计划"""
        return self._plan_simple_query(intent)
    
    def _adjust_parameters(self, tool_call: ToolCall, context: Dict) -> ToolCall:
        """动态调整工具调用参数（简化版本）"""
        # 🔧 简化处理，直接返回原始调用，避免复杂的动态逻辑出错
        return ToolCall(
            tool_name=tool_call.tool_name,
            parameters=tool_call.parameters.copy(),
            step_description=tool_call.step_description,
            depends_on=tool_call.depends_on
        )
    
    def _execute_tool_with_retry(self, tool_call: ToolCall, step_index: int) -> ToolResult:
        """带重试的工具执行"""
        last_error = None
        
        for attempt in range(self.max_retries + 1):
            try:
                start_time = time.time()
                
                if tool_call.tool_name not in self.tool_map:
                    return ToolResult(
                        success=False,
                        result=None,
                        error=f"工具 {tool_call.tool_name} 不存在"
                    )
                
                tool = self.tool_map[tool_call.tool_name]
                result = tool.call(**tool_call.parameters)
                
                duration = time.time() - start_time
                
                return ToolResult(
                    success=result.success,
                    result=result.result,
                    error=result.error,
                    timing={"duration": duration, "attempt": attempt + 1}
                )
                
            except Exception as e:
                last_error = str(e)
                logger.warning(f"⚠️ [工具编排] 步骤 {step_index+1} 第 {attempt+1} 次尝试失败: {last_error}")
                
                if attempt < self.max_retries:
                    time.sleep(1)  # 简单的退避策略
        
        return ToolResult(
            success=False,
            result=None,
            error=f"重试 {self.max_retries} 次后仍然失败: {last_error}"
        )
    
    def _check_dependencies(self, depends_on: List[int], results: List[ToolResult]) -> bool:
        """检查依赖是否满足"""
        for dep_index in depends_on:
            if dep_index >= len(results) or not results[dep_index].success:
                return False
        return True
    
    def _update_execution_context(self, result: ToolResult, context: Dict):
        """更新执行上下文"""
        if result.tool_call.tool_name == "integrated_sql" and result.success:
            # 提取SQL查询结果中的关键信息
            if isinstance(result.result, dict):
                execution_result = result.result.get("execution_result", {})
                if "data" in execution_result:
                    context["extracted_data"]["last_sql_data"] = execution_result["data"]
                if "columns" in execution_result:
                    context["extracted_data"]["last_sql_columns"] = execution_result["columns"]
    
    def _extract_indicators_from_context(self, context: Dict) -> List[str]:
        """从上下文中提取指标列表"""
        # 简化实现，实际应该解析SQL结果
        return ["转供电非正规发票", "预付费超期未核销", "已报账缴费单超过合同约定单价"]
    
    def _extract_top_risk_from_context(self, context: Dict) -> str:
        """从上下文中提取最大风险"""
        # 简化实现，实际应该分析SQL结果找出金额最大的
        return "预付费超期未核销"
    
    def _should_continue_on_error(self, failed_result: ToolResult, plan: ExecutionPlan) -> bool:
        """判断错误后是否继续执行"""
        # 如果是关键的第一步失败，停止执行
        if failed_result.step_index == 0:
            return False
        
        # 其他情况可以继续
        return True

    def _plan_from_complex_prompt(self, intent: IntentResult, complex_prompt) -> ExecutionPlan:
        """
        🎯 基于复杂提示词规划执行计划

        这是关键方法：让V2架构能够使用复杂提示词的内容！

        Args:
            complex_prompt: ComplexPrompt对象
        """
        logger.info(f"🎯 [工具编排] 使用复杂提示词规划: {complex_prompt.title}")

        # 提取复杂提示词的内容
        prompt_content = "\n".join(complex_prompt.processing_steps)
        prompt_query = complex_prompt.title

        tool_calls = []

        # 🎯 智能解析复杂提示词中的具体步骤
        tool_calls = self._parse_prompt_steps(prompt_content, intent)

        # 如果解析失败，回退到简单模式
        if not tool_calls:
            logger.info("🔄 [工具编排] 提示词解析失败，使用默认查询逻辑")
            tool_calls.append(ToolCall(
                tool_name="integrated_sql",
                parameters={
                    "question": prompt_query or f"{intent.time_range or '2025年7月'}网络电费风险情况",
                    "target_database": intent.province_code or "GX"
                },
                step_description=f"基于提示词查询: {prompt_query[:50]}..."
            ))

        return ExecutionPlan(
            tool_calls=tool_calls,
            total_steps=len(tool_calls),
            estimated_duration=len(tool_calls) * 3.0,
            requires_sequential=True
        )

    def _extract_sql_questions_from_prompt(self, prompt_content: str, intent: IntentResult) -> List[str]:
        """从复杂提示词中提取SQL查询问题"""
        questions = []

        # 简单的关键词匹配提取（可以后续优化为更智能的解析）
        if "风险" in prompt_content:
            questions.append(f"{intent.time_range or '2025年7月'}网络电费风险情况")

        if "预付费" in prompt_content:
            questions.append(f"{intent.time_range or '2025年7月'}预付费超期未核销情况")

        if "转供电" in prompt_content:
            questions.append(f"{intent.time_range or '2025年7月'}转供电非正规发票情况")

        if "合同单价" in prompt_content:
            questions.append(f"{intent.time_range or '2025年7月'}已报账缴费单超过合同约定单价情况")

        # 如果没有匹配到具体问题，使用通用查询
        if not questions:
            questions.append(f"{intent.time_range or '2025年7月'}网络电费相关数据")

        return questions

    def _extract_knowledge_queries_from_prompt(self, prompt_content: str, intent: IntentResult) -> List[str]:
        """从复杂提示词中提取知识搜索查询"""
        queries = []

        if "计算逻辑" in prompt_content:
            queries.append("电费计算逻辑和方法")

        if "风险管理" in prompt_content:
            queries.append("网络电费风险管理建议")

        if "业务规则" in prompt_content:
            queries.append("电费管理业务规则和流程")

        return queries

    def _parse_prompt_steps(self, prompt_content: str, intent: IntentResult) -> List[ToolCall]:
        """
        🎯 智能解析复杂提示词中的具体步骤

        这个方法会严格按照复杂提示词中的步骤执行，不添加额外的调用
        """
        tool_calls = []

        # 按行分割提示词内容
        lines = prompt_content.split('\n')

        step_number = 0
        for line in lines:
            line = line.strip()
            if not line:
                continue

            # 检查是否是步骤行
            if any(keyword in line for keyword in ['调用integrated_sql', 'SQL工具', '查询']):
                if 'integrated_sql' in line or 'SQL' in line:
                    # 提取查询内容
                    query = self._extract_query_from_step(line, intent)
                    if query:
                        tool_calls.append(ToolCall(
                            tool_name="integrated_sql",
                            parameters={
                                "question": query,
                                "target_database": intent.province_code or "GZ"
                            },
                            step_description=f"步骤{step_number + 1}: {query[:50]}...",
                            depends_on=[step_number - 1] if step_number > 0 else []
                        ))
                        step_number += 1

            elif any(keyword in line for keyword in ['调用knowledge_search', '知识搜索', 'knowledge_search工具']):
                # 提取知识搜索内容
                query = self._extract_knowledge_query_from_step(line, intent)
                if query:
                    tool_calls.append(ToolCall(
                        tool_name="knowledge_search",
                        parameters={"query": query},
                        step_description=f"步骤{step_number + 1}: 搜索{query[:30]}...",
                        depends_on=[]  # 知识搜索可以并行
                    ))
                    step_number += 1

        # 如果没有解析到明确的步骤，尝试从整体内容推断
        if not tool_calls:
            tool_calls = self._fallback_parse_prompt(prompt_content, intent)

        logger.info(f"🎯 [工具编排] 从复杂提示词解析出 {len(tool_calls)} 个执行步骤")
        return tool_calls

    def _extract_query_from_step(self, step_line: str, intent: IntentResult) -> str:
        """从步骤行中提取查询内容"""

        # 查找引号中的内容
        import re

        # 匹配双引号中的内容
        quote_match = re.search(r'"([^"]+)"', step_line)
        if quote_match:
            return quote_match.group(1)

        # 匹配单引号中的内容
        quote_match = re.search(r"'([^']+)'", step_line)
        if quote_match:
            return quote_match.group(1)

        # 如果没有引号，尝试提取关键信息
        if "风险情况" in step_line:
            return f"{intent.time_range or '2025年7月'}网络电费风险情况"
        elif "风险指标" in step_line:
            return f"{intent.time_range or '2025年7月'}风险指标详情"

        return None

    def _extract_knowledge_query_from_step(self, step_line: str, intent: IntentResult) -> str:
        """从步骤行中提取知识搜索内容"""

        import re

        # 查找引号中的内容
        quote_match = re.search(r'"([^"]+)"', step_line)
        if quote_match:
            return quote_match.group(1)

        quote_match = re.search(r"'([^']+)'", step_line)
        if quote_match:
            return quote_match.group(1)

        # 如果没有引号，尝试提取关键信息
        if "改进建议" in step_line:
            return "风险管理改进建议"
        elif "优化" in step_line:
            return "电费管理优化建议"

        return None

    def _fallback_parse_prompt(self, prompt_content: str, intent: IntentResult) -> List[ToolCall]:
        """回退解析方法"""
        tool_calls = []

        # 如果包含风险相关内容，添加基础查询
        if "风险" in prompt_content:
            tool_calls.append(ToolCall(
                tool_name="integrated_sql",
                parameters={
                    "question": f"{intent.time_range or '2025年7月'}网络电费风险情况",
                    "target_database": intent.province_code or "GZ"
                },
                step_description="基础风险查询"
            ))

        # 只有在明确提到知识搜索时才添加
        if "knowledge_search" in prompt_content or "知识搜索" in prompt_content:
            tool_calls.append(ToolCall(
                tool_name="knowledge_search",
                parameters={"query": "网络电费风险管理建议"},
                step_description="搜索管理建议",
                depends_on=[]
            ))

        return tool_calls
