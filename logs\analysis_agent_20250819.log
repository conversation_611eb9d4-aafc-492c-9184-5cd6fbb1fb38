2025-08-19 09:09:45,901 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-19 09:09:46,049 - src.core.prompt_vectorstore_manager - INFO - 🌐 使用远程embedding模型
2025-08-19 09:09:46,050 - src.core.prompt_vectorstore_manager - INFO - 📡 服务地址: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-19 09:09:46,050 - src.core.prompt_vectorstore_manager - INFO - 🤖 模型名称: bge-large-zh-v1.5
2025-08-19 09:09:46,052 - src.core.remote_embeddings - INFO - 🔧 使用自定义embedding服务
2025-08-19 09:09:46,052 - src.core.remote_embeddings - INFO - 🌐 初始化远程Embedding服务: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-19 09:09:46,069 - src.core.remote_embeddings - INFO - 📝 使用模型: bge-large-zh-v1.5
2025-08-19 09:09:46,070 - src.core.remote_embeddings - INFO - 🔧 测试远程embedding服务连接...
2025-08-19 09:09:46,575 - src.core.remote_embeddings - INFO - ✅ 远程embedding服务连接成功，向量维度: 1024
2025-08-19 09:09:47,791 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-19 09:09:49,045 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化成功: ./data/prompt_chroma_db
2025-08-19 09:09:49,046 - src.core.prompt_vectorstore_manager - INFO - 📊 数据库中现有复杂提示词数量: 8
2025-08-19 09:09:49,080 - src.core.prompt_vectorstore_manager - INFO - 📝 数据库中的示例数据: 3 条
2025-08-19 09:09:49,081 - src.core.prompt_vectorstore_manager - INFO -   - 863114a7-f632-4260-ad88-5a6606de92bc: 电费情况综合分析
2025-08-19 09:09:49,082 - src.core.prompt_vectorstore_manager - INFO -   - b1061559-0ebb-47e6-ab8a-68b1bda2316f: 电费情况综合分析
2025-08-19 09:09:49,082 - src.core.prompt_vectorstore_manager - INFO -   - 2e68db3b-efdb-4d6c-8530-b14c4f723dcb: 电费情况综合分析
2025-08-19 09:09:49,083 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化完成
2025-08-19 09:09:49,083 - src.agents.analysis_agent_v2 - INFO - 🏗️ [AnalysisAgentV2] 组件化架构初始化完成
2025-08-19 09:09:49,084 - src.agents.analysis_agent_v2 - INFO - 🔧 [AnalysisAgentV2] V2架构 启用
2025-08-19 09:09:49,084 - __main__ - INFO - 🚀 [服务器] 启用AnalysisAgentV2架构，永远相信vanna服务
2025-08-19 09:09:49,085 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-19 09:09:49,085 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-19 09:09:49,086 - __main__ - INFO - 🚀 Analysis Agent API 服务器启动
2025-08-19 09:09:49,087 - __main__ - INFO - 🌐 监听地址: 0.0.0.0:8001
2025-08-19 09:09:49,260 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-19 09:09:49,262 - src.agents.analysis_agent_v2 - INFO - 🏗️ [AnalysisAgentV2] 组件化架构初始化完成
2025-08-19 09:09:49,262 - src.agents.analysis_agent_v2 - INFO - 🔧 [AnalysisAgentV2] V2架构 启用
2025-08-19 09:09:49,263 - __main__ - INFO - 🚀 [服务器] 启用AnalysisAgentV2架构，永远相信vanna服务
2025-08-19 09:09:49,263 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-19 09:09:49,264 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-19 09:10:09,512 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-19 09:10:09,514 - src.core.prompt_vectorstore_manager - INFO - 🌐 使用远程embedding模型
2025-08-19 09:10:09,515 - src.core.prompt_vectorstore_manager - INFO - 📡 服务地址: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-19 09:10:09,515 - src.core.prompt_vectorstore_manager - INFO - 🤖 模型名称: bge-large-zh-v1.5
2025-08-19 09:10:09,517 - src.core.remote_embeddings - INFO - 🔧 使用自定义embedding服务
2025-08-19 09:10:09,518 - src.core.remote_embeddings - INFO - 🌐 初始化远程Embedding服务: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-19 09:10:09,519 - src.core.remote_embeddings - INFO - 📝 使用模型: bge-large-zh-v1.5
2025-08-19 09:10:09,519 - src.core.remote_embeddings - INFO - 🔧 测试远程embedding服务连接...
2025-08-19 09:10:09,804 - src.core.remote_embeddings - INFO - ✅ 远程embedding服务连接成功，向量维度: 1024
2025-08-19 09:10:10,286 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-19 09:10:10,602 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化成功: ./data/prompt_chroma_db
2025-08-19 09:10:10,602 - src.core.prompt_vectorstore_manager - INFO - 📊 数据库中现有复杂提示词数量: 8
2025-08-19 09:10:10,624 - src.core.prompt_vectorstore_manager - INFO - 📝 数据库中的示例数据: 3 条
2025-08-19 09:10:10,625 - src.core.prompt_vectorstore_manager - INFO -   - 863114a7-f632-4260-ad88-5a6606de92bc: 电费情况综合分析
2025-08-19 09:10:10,625 - src.core.prompt_vectorstore_manager - INFO -   - b1061559-0ebb-47e6-ab8a-68b1bda2316f: 电费情况综合分析
2025-08-19 09:10:10,626 - src.core.prompt_vectorstore_manager - INFO -   - 2e68db3b-efdb-4d6c-8530-b14c4f723dcb: 电费情况综合分析
2025-08-19 09:10:10,627 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化完成
2025-08-19 09:10:10,628 - src.agents.analysis_agent_v2 - INFO - 🏗️ [AnalysisAgentV2] 组件化架构初始化完成
2025-08-19 09:10:10,629 - src.agents.analysis_agent_v2 - INFO - 🔧 [AnalysisAgentV2] V2架构 启用
2025-08-19 09:10:10,631 - __main__ - INFO - 🚀 [服务器] 启用AnalysisAgentV2架构，永远相信vanna服务
2025-08-19 09:10:10,632 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-19 09:10:10,633 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-19 09:10:10,633 - __main__ - INFO - 🚀 Analysis Agent API 服务器启动
2025-08-19 09:10:10,634 - __main__ - INFO - 🌐 监听地址: 0.0.0.0:8001
2025-08-19 09:10:10,694 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-19 09:10:10,695 - src.agents.analysis_agent_v2 - INFO - 🏗️ [AnalysisAgentV2] 组件化架构初始化完成
2025-08-19 09:10:10,696 - src.agents.analysis_agent_v2 - INFO - 🔧 [AnalysisAgentV2] V2架构 启用
2025-08-19 09:10:10,696 - __main__ - INFO - 🚀 [服务器] 启用AnalysisAgentV2架构，永远相信vanna服务
2025-08-19 09:10:10,697 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-19 09:10:10,698 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-19 09:10:59,753 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-19 09:10:59,755 - src.core.prompt_vectorstore_manager - INFO - 🌐 使用远程embedding模型
2025-08-19 09:10:59,756 - src.core.prompt_vectorstore_manager - INFO - 📡 服务地址: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-19 09:10:59,756 - src.core.prompt_vectorstore_manager - INFO - 🤖 模型名称: bge-large-zh-v1.5
2025-08-19 09:10:59,758 - src.core.remote_embeddings - INFO - 🔧 使用自定义embedding服务
2025-08-19 09:10:59,758 - src.core.remote_embeddings - INFO - 🌐 初始化远程Embedding服务: http://ai.ai.iot.chinamobile.com/imaas/v1/embeddings
2025-08-19 09:10:59,759 - src.core.remote_embeddings - INFO - 📝 使用模型: bge-large-zh-v1.5
2025-08-19 09:10:59,761 - src.core.remote_embeddings - INFO - 🔧 测试远程embedding服务连接...
2025-08-19 09:11:00,413 - src.core.remote_embeddings - INFO - ✅ 远程embedding服务连接成功，向量维度: 1024
2025-08-19 09:11:00,907 - chromadb.telemetry.product.posthog - INFO - Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-08-19 09:11:01,176 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化成功: ./data/prompt_chroma_db
2025-08-19 09:11:01,177 - src.core.prompt_vectorstore_manager - INFO - 📊 数据库中现有复杂提示词数量: 8
2025-08-19 09:11:01,198 - src.core.prompt_vectorstore_manager - INFO - 📝 数据库中的示例数据: 3 条
2025-08-19 09:11:01,198 - src.core.prompt_vectorstore_manager - INFO -   - 863114a7-f632-4260-ad88-5a6606de92bc: 电费情况综合分析
2025-08-19 09:11:01,199 - src.core.prompt_vectorstore_manager - INFO -   - b1061559-0ebb-47e6-ab8a-68b1bda2316f: 电费情况综合分析
2025-08-19 09:11:01,199 - src.core.prompt_vectorstore_manager - INFO -   - 2e68db3b-efdb-4d6c-8530-b14c4f723dcb: 电费情况综合分析
2025-08-19 09:11:01,200 - src.core.prompt_vectorstore_manager - INFO - ✅ 复杂提示词向量数据库初始化完成
2025-08-19 09:11:01,200 - src.agents.analysis_agent_v2 - INFO - 🏗️ [AnalysisAgentV2] 组件化架构初始化完成
2025-08-19 09:11:01,201 - src.agents.analysis_agent_v2 - INFO - 🔧 [AnalysisAgentV2] V2架构 启用
2025-08-19 09:11:01,201 - __main__ - INFO - 🚀 [服务器] 启用AnalysisAgentV2架构，永远相信vanna服务
2025-08-19 09:11:01,201 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-19 09:11:01,202 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-19 09:11:01,203 - __main__ - INFO - 🚀 Analysis Agent API 服务器启动
2025-08-19 09:11:01,203 - __main__ - INFO - 🌐 监听地址: 0.0.0.0:8001
2025-08-19 09:11:01,255 - __main__ - INFO - 🤖 初始化SQL Agent...
2025-08-19 09:11:01,256 - src.agents.analysis_agent_v2 - INFO - 🏗️ [AnalysisAgentV2] 组件化架构初始化完成
2025-08-19 09:11:01,257 - src.agents.analysis_agent_v2 - INFO - 🔧 [AnalysisAgentV2] V2架构 启用
2025-08-19 09:11:01,257 - __main__ - INFO - 🚀 [服务器] 启用AnalysisAgentV2架构，永远相信vanna服务
2025-08-19 09:11:01,257 - __main__ - INFO - ✅ SQL Agent初始化成功，工具数量: 2
2025-08-19 09:11:01,257 - __main__ - INFO - 📋 可用工具: ['integrated_sql', 'knowledge_search']
2025-08-19 09:11:26,094 - __main__ - INFO - 🚀 [API调用] /v1/chat/completions 端点被调用
2025-08-19 09:11:26,099 - __main__ - INFO - 👤 [用户输入] '网络电费7月存在哪些风险'
2025-08-19 09:11:26,100 - __main__ - INFO - 📚 [对话历史] 0 条历史记录
2025-08-19 09:11:26,101 - __main__ - INFO - 🔧 [最终消息] Agent将处理: '网络电费7月存在哪些风险'
2025-08-19 09:11:26,102 - src.agents.analysis_agent_v2 - INFO - 🤖 [AnalysisAgentV2] 开始处理用户消息: 网络电费7月存在哪些风险
2025-08-19 09:11:26,103 - src.agents.vector_enhanced_agent - INFO - 🎯 [问题提取] 未找到特定格式，使用原始消息: 网络电费7月存在哪些风险
2025-08-19 09:11:26,103 - src.agents.vector_enhanced_agent - INFO - 🔍 [向量搜索] 搜索查询: 网络电费7月存在哪些风险
2025-08-19 09:11:26,104 - src.core.prompt_vectorstore_manager - INFO - 🔍 开始搜索复杂提示词，数据库中共有 8 条数据
2025-08-19 09:11:26,105 - src.core.prompt_vectorstore_manager - INFO - 🔍 搜索查询: '网络电费7月存在哪些风险', k=3, score_threshold=0.95
2025-08-19 09:11:26,606 - src.core.prompt_vectorstore_manager - INFO - 🔍 向量搜索返回 8 个原始结果
2025-08-19 09:11:26,607 - src.core.prompt_vectorstore_manager - INFO - 🔍 搜索到 0 个匹配的复杂提示词
2025-08-19 09:11:26,607 - src.agents.vector_enhanced_agent - INFO - ❌ [向量搜索] 未找到匹配的复杂提示词
2025-08-19 09:11:26,608 - src.agents.components.intent_analyzer - INFO - 🔍 [意图分析] 开始分析用户输入: 网络电费7月存在哪些风险
2025-08-19 09:11:26,609 - src.agents.components.intent_analyzer - INFO - 🎯 [意图分析] 提取的用户问题: 网络电费7月存在哪些风险
2025-08-19 09:11:26,610 - src.agents.components.intent_analyzer - INFO - ✅ [意图分析] 分析完成: complex_analysis, 置信度: 0.950, 多步骤: True
2025-08-19 09:11:26,610 - src.agents.analysis_agent_v2 - INFO - 🎯 [AnalysisAgentV2] 意图分析完成 (用时: 0.51秒)
2025-08-19 09:11:26,611 - src.agents.components.tool_orchestrator - INFO - 📋 [工具编排] 开始规划执行计划，意图类型: complex_analysis
2025-08-19 09:11:26,611 - src.agents.components.tool_orchestrator - INFO - ✅ [工具编排] 执行计划完成，共 5 个步骤
2025-08-19 09:11:26,612 - src.agents.components.tool_orchestrator - INFO - 🚀 [工具编排] 开始执行计划，共 5 个步骤
2025-08-19 09:11:26,612 - src.agents.components.tool_orchestrator - INFO - 🔧 [工具编排] 执行步骤 1/5: 查询总体风险情况
2025-08-19 09:11:26,613 - src.tools.integrated_sql_tools - INFO - 🔧 [VANNA调用] 开始生成SQL
2025-08-19 09:11:26,613 - src.tools.integrated_sql_tools - INFO - 🔧 [VANNA调用] 传递的问题: '2025年7月网络电费风险情况'
2025-08-19 09:11:26,614 - src.tools.integrated_sql_tools - INFO - 🔧 [VANNA调用] API地址: http://10.12.22.20:5000/api/v0/generate_sql
2025-08-19 09:11:26,614 - src.tools.integrated_sql_tools - INFO - 🌐 [VANNA调用] 发送API请求...
2025-08-19 09:11:26,615 - src.tools.integrated_sql_tools - INFO - 🌐 [VANNA调用] 请求参数: question='2025年7月网络电费风险情况'
2025-08-19 09:11:26,615 - src.tools.integrated_sql_tools - INFO - 🌐 [VANNA调用] 请求头: {'Accept': 'application/json, text/plain, */*', 'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6', 'Cache-Control': 'no-cache', 'Connection': 'keep-alive', 'Content-Type': 'application/json', 'X-API-Key': 'vn_live_8f4b5c2937e6d4a1b8f3e2c9d8a7b6e5f4c3b2a1', 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
2025-08-19 09:11:26,616 - src.tools.integrated_sql_tools - INFO - 🌐 [VANNA调用] 完整URL: http://10.12.22.20:5000/api/v0/generate_sql?question=2025年7月网络电费风险情况
2025-08-19 09:11:27,511 - src.tools.integrated_sql_tools - INFO - 🌐 [VANNA调用] API响应状态码: 200 (请求用时: 0.90秒)
2025-08-19 09:11:27,512 - src.tools.integrated_sql_tools - INFO - 📥 [VANNA调用] API返回数据结构: ['id', 'text', 'type']
2025-08-19 09:11:27,513 - src.tools.integrated_sql_tools - INFO - 📥 [VANNA调用] 返回的text字段预览: SELECT 
    rpt_month AS '年月',
    CASE risk_type 
        WHEN 1 THEN '转供电非正规发票'
        WHEN 2 THEN '预付费超期未核销'
        WHEN 3 THEN '当前折扣大于历史折扣'
        WHEN 4 THEN '同一订单不同月份铁塔类型不一致'
        WHEN 5 T...
2025-08-19 09:11:27,514 - src.tools.integrated_sql_tools - INFO - 📥 [VANNA调用] text字段类型: sql
2025-08-19 09:11:27,514 - src.tools.integrated_sql_tools - INFO - ✅ [VANNA调用] 使用新格式解析，type=sql
2025-08-19 09:11:27,515 - src.tools.integrated_sql_tools - INFO - ✅ [VANNA调用] 成功生成SQL (总用时: 0.90秒)
2025-08-19 09:11:27,515 - src.tools.integrated_sql_tools - INFO - ✅ [VANNA调用] 生成的SQL: SELECT 
    rpt_month AS '年月',
    CASE risk_type 
        WHEN 1 THEN '转供电非正规发票'
        WHEN 2 THEN '预付费超期未核销'
        WHEN 3 THEN '当前折扣大于历史折扣'
        WHEN 4 THEN '同一订单不同月份铁塔类型不一致'
        WHEN 5 THEN '同一订单不同月份机房类型不一致'
        WHEN 6 THEN '同一订单不同月份配套类型不一致'
        WHEN 7 THEN '订单挂高大于塔高'
        WHEN 8 THEN '同一物理站址多次计取维护费'
        WHEN 9 THEN '资源异常预警工单超时'
        WHEN 10 THEN '代维按次工单费用为空'
        WHEN 11 THEN '代维按次工单派单资源不在综资系统中存在'
        WHEN 12 THEN '共享数量与分摊比例不匹配'
        WHEN 13 THEN '已报账缴费单超过合同约定单价'
        WHEN 14 THEN '转改直期间直供电按转供电缴费'
        WHEN 15 THEN '已报账缴费单关联资源退网'
        ELSE '未知类型'
    END AS '指标',
    check_result AS '条数',
    ROUND(involve_amount/10000,2) AS '金额(万元)'
FROM rpt_risk_board 
WHERE rpt_month = '2025-07'
and risk_type in (1,2,13)
2025-08-19 09:11:27,517 - src.tools.integrated_sql_tools - INFO - 🔧 [SQL执行] 开始执行SQL查询
2025-08-19 09:11:27,517 - src.tools.integrated_sql_tools - INFO - 🔧 [SQL执行] 数据库类型: mysql
2025-08-19 09:11:27,518 - src.tools.integrated_sql_tools - INFO - 🔧 [SQL执行] 原始SQL语句: SELECT 
    rpt_month AS '年月',
    CASE risk_type 
        WHEN 1 THEN '转供电非正规发票'
        WHEN 2 THEN '预付费超期未核销'
        WHEN 3 THEN '当前折扣大于历史折扣'
        WHEN 4 THEN '同一订单不同月份铁塔类型不一致'
        WHEN 5 THEN '同一订单不同月份机房类型不一致'
        WHEN 6 THEN '同一订单不同月份配套类型不一致'
        WHEN 7 THEN '订单挂高大于塔高'
        WHEN 8 THEN '同一物理站址多次计取维护费'
        WHEN 9 THEN '资源异常预警工单超时'
        WHEN 10 THEN '代维按次工单费用为空'
        WHEN 11 THEN '代维按次工单派单资源不在综资系统中存在'
        WHEN 12 THEN '共享数量与分摊比例不匹配'
        WHEN 13 THEN '已报账缴费单超过合同约定单价'
        WHEN 14 THEN '转改直期间直供电按转供电缴费'
        WHEN 15 THEN '已报账缴费单关联资源退网'
        ELSE '未知类型'
    END AS '指标',
    check_result AS '条数',
    ROUND(involve_amount/10000,2) AS '金额(万元)'
FROM rpt_risk_board 
WHERE rpt_month = '2025-07'
and risk_type in (1,2,13)
2025-08-19 09:11:27,580 - src.tools.integrated_sql_tools - INFO - 📊 [SQL工具] ✅ SQL查询完成，返回 3 条记录
2025-08-19 09:11:27,581 - src.agents.components.tool_orchestrator - INFO - ✅ [工具编排] 步骤 1 执行成功
2025-08-19 09:11:27,582 - src.agents.components.tool_orchestrator - INFO - 🔧 [工具编排] 执行步骤 2/5: 查询预付费超期未核销详情
2025-08-19 09:11:27,583 - src.tools.integrated_sql_tools - INFO - 🔧 [VANNA调用] 开始生成SQL
2025-08-19 09:11:27,583 - src.tools.integrated_sql_tools - INFO - 🔧 [VANNA调用] 传递的问题: '2025年7月风险指标预付费超期未核销情况'
2025-08-19 09:11:27,584 - src.tools.integrated_sql_tools - INFO - 🔧 [VANNA调用] API地址: http://10.12.22.20:5000/api/v0/generate_sql
2025-08-19 09:11:27,584 - src.tools.integrated_sql_tools - INFO - 🌐 [VANNA调用] 发送API请求...
2025-08-19 09:11:27,585 - src.tools.integrated_sql_tools - INFO - 🌐 [VANNA调用] 请求参数: question='2025年7月风险指标预付费超期未核销情况'
2025-08-19 09:11:27,585 - src.tools.integrated_sql_tools - INFO - 🌐 [VANNA调用] 请求头: {'Accept': 'application/json, text/plain, */*', 'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6', 'Cache-Control': 'no-cache', 'Connection': 'keep-alive', 'Content-Type': 'application/json', 'X-API-Key': 'vn_live_8f4b5c2937e6d4a1b8f3e2c9d8a7b6e5f4c3b2a1', 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
2025-08-19 09:11:27,586 - src.tools.integrated_sql_tools - INFO - 🌐 [VANNA调用] 完整URL: http://10.12.22.20:5000/api/v0/generate_sql?question=2025年7月风险指标预付费超期未核销情况
2025-08-19 09:11:28,475 - src.tools.integrated_sql_tools - INFO - 🌐 [VANNA调用] API响应状态码: 200 (请求用时: 0.89秒)
2025-08-19 09:11:28,476 - src.tools.integrated_sql_tools - INFO - 📥 [VANNA调用] API返回数据结构: ['id', 'text', 'type']
2025-08-19 09:11:28,477 - src.tools.integrated_sql_tools - INFO - 📥 [VANNA调用] 返回的text字段预览: SELECT
	el.preg_name as '地市',
	el.reg_name as '区县',
	el.billaccount_code as '报账点编码',
	el.loan_code as '缴费单编码',
	elb.billamount_code as '预付费汇总编号',
	elb.finance_date as '财务审核通过日期',
	CASE
		e.type WHEN 1...
2025-08-19 09:11:28,478 - src.tools.integrated_sql_tools - INFO - 📥 [VANNA调用] text字段类型: sql
2025-08-19 09:11:28,478 - src.tools.integrated_sql_tools - INFO - ✅ [VANNA调用] 使用新格式解析，type=sql
2025-08-19 09:11:28,479 - src.tools.integrated_sql_tools - INFO - ✅ [VANNA调用] 成功生成SQL (总用时: 0.90秒)
2025-08-19 09:11:28,479 - src.tools.integrated_sql_tools - INFO - ✅ [VANNA调用] 生成的SQL: SELECT
	el.preg_name as '地市',
	el.reg_name as '区县',
	el.billaccount_code as '报账点编码',
	el.loan_code as '缴费单编码',
	elb.billamount_code as '预付费汇总编号',
	elb.finance_date as '财务审核通过日期',
	CASE
		e.type WHEN 1 THEN '普通预付费'
		WHEN 2 THEN 'IC卡预付费'
		WHEN 3 THEN '先款后票预付费'
	END AS '预付费类型',
	CASE
		IFNULL(el.billaccount_type, eb.billaccount_type) WHEN 1 THEN '自维电费报账点'
		WHEN 2 THEN '铁塔电费报账点'
		WHEN 3 THEN '代持电费报账点'
	END AS '报账点类型',
	el.billaccount_name as '报账点名称',
	el.contract_code as '合同编码',
	el.contract_name as '合同名称',
	el.loan_date as '借款申请日期',
	el.loan_money as '借款金额',
	evl.verification_amount as '已核销金额'
FROM
	clean_ele_loan el
LEFT JOIN ele_loan_billamount elb ON
	el.billamount_id = elb.billamount_id
LEFT JOIN ele_loan_balance e ON
	el.billaccount_id = e.billaccount_id
left join clean_ele_billaccount eb on
	el.billaccount_id = eb.billaccount_id
LEFT JOIN (
	SELECT
		SUM(verification_amount) verification_amount,
		loan_id
	from
		clean_ele_verification_loan
	GROUP BY
		loan_id) evl on
	el.loan_id = evl.loan_id
where
	case
		e.type WHEN 1 THEN loan_money - IFNULL(verification_amount, 0) > 0
		and el.loan_date < DATE_SUB(CURDATE(), INTERVAL 6 MONTH)
		else loan_money - IFNULL(verification_amount, 0) > 0
		AND elb.finance_date < DATE_SUB(CURDATE(), INTERVAL 3 MONTH)
	END
	AND '2025-08-01' > el.loan_date
	and el.loan_state != -1
	AND el.is_finance = 1
	and el.data_state = 0
	AND elb.billamount_state = 2
	limit 10
2025-08-19 09:11:28,482 - src.tools.integrated_sql_tools - INFO - 🔧 [SQL执行] 开始执行SQL查询
2025-08-19 09:11:28,483 - src.tools.integrated_sql_tools - INFO - 🔧 [SQL执行] 数据库类型: mysql
2025-08-19 09:11:28,483 - src.tools.integrated_sql_tools - INFO - 🔧 [SQL执行] 原始SQL语句: SELECT
	el.preg_name as '地市',
	el.reg_name as '区县',
	el.billaccount_code as '报账点编码',
	el.loan_code as '缴费单编码',
	elb.billamount_code as '预付费汇总编号',
	elb.finance_date as '财务审核通过日期',
	CASE
		e.type WHEN 1 THEN '普通预付费'
		WHEN 2 THEN 'IC卡预付费'
		WHEN 3 THEN '先款后票预付费'
	END AS '预付费类型',
	CASE
		IFNULL(el.billaccount_type, eb.billaccount_type) WHEN 1 THEN '自维电费报账点'
		WHEN 2 THEN '铁塔电费报账点'
		WHEN 3 THEN '代持电费报账点'
	END AS '报账点类型',
	el.billaccount_name as '报账点名称',
	el.contract_code as '合同编码',
	el.contract_name as '合同名称',
	el.loan_date as '借款申请日期',
	el.loan_money as '借款金额',
	evl.verification_amount as '已核销金额'
FROM
	clean_ele_loan el
LEFT JOIN ele_loan_billamount elb ON
	el.billamount_id = elb.billamount_id
LEFT JOIN ele_loan_balance e ON
	el.billaccount_id = e.billaccount_id
left join clean_ele_billaccount eb on
	el.billaccount_id = eb.billaccount_id
LEFT JOIN (
	SELECT
		SUM(verification_amount) verification_amount,
		loan_id
	from
		clean_ele_verification_loan
	GROUP BY
		loan_id) evl on
	el.loan_id = evl.loan_id
where
	case
		e.type WHEN 1 THEN loan_money - IFNULL(verification_amount, 0) > 0
		and el.loan_date < DATE_SUB(CURDATE(), INTERVAL 6 MONTH)
		else loan_money - IFNULL(verification_amount, 0) > 0
		AND elb.finance_date < DATE_SUB(CURDATE(), INTERVAL 3 MONTH)
	END
	AND '2025-08-01' > el.loan_date
	and el.loan_state != -1
	AND el.is_finance = 1
	and el.data_state = 0
	AND elb.billamount_state = 2
	limit 10
2025-08-19 09:11:28,553 - src.tools.integrated_sql_tools - INFO - 📊 [SQL工具] ✅ SQL查询完成，返回 10 条记录
2025-08-19 09:11:28,553 - src.agents.components.tool_orchestrator - INFO - ✅ [工具编排] 步骤 2 执行成功
2025-08-19 09:11:28,554 - src.agents.components.tool_orchestrator - INFO - 🔧 [工具编排] 执行步骤 3/5: 查询转供电非正规发票详情
2025-08-19 09:11:28,554 - src.tools.integrated_sql_tools - INFO - 🔧 [VANNA调用] 开始生成SQL
2025-08-19 09:11:28,555 - src.tools.integrated_sql_tools - INFO - 🔧 [VANNA调用] 传递的问题: '2025年7月风险指标转供电非正规发票情况'
2025-08-19 09:11:28,555 - src.tools.integrated_sql_tools - INFO - 🔧 [VANNA调用] API地址: http://10.12.22.20:5000/api/v0/generate_sql
2025-08-19 09:11:28,556 - src.tools.integrated_sql_tools - INFO - 🌐 [VANNA调用] 发送API请求...
2025-08-19 09:11:28,556 - src.tools.integrated_sql_tools - INFO - 🌐 [VANNA调用] 请求参数: question='2025年7月风险指标转供电非正规发票情况'
2025-08-19 09:11:28,557 - src.tools.integrated_sql_tools - INFO - 🌐 [VANNA调用] 请求头: {'Accept': 'application/json, text/plain, */*', 'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6', 'Cache-Control': 'no-cache', 'Connection': 'keep-alive', 'Content-Type': 'application/json', 'X-API-Key': 'vn_live_8f4b5c2937e6d4a1b8f3e2c9d8a7b6e5f4c3b2a1', 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
2025-08-19 09:11:28,557 - src.tools.integrated_sql_tools - INFO - 🌐 [VANNA调用] 完整URL: http://10.12.22.20:5000/api/v0/generate_sql?question=2025年7月风险指标转供电非正规发票情况
2025-08-19 09:11:29,598 - src.tools.integrated_sql_tools - INFO - 🌐 [VANNA调用] API响应状态码: 200 (请求用时: 1.04秒)
2025-08-19 09:11:29,599 - src.tools.integrated_sql_tools - INFO - 📥 [VANNA调用] API返回数据结构: ['id', 'text', 'type']
2025-08-19 09:11:29,599 - src.tools.integrated_sql_tools - INFO - 📥 [VANNA调用] 返回的text字段预览: SELECT
	DISTINCT CONCAT(YEAR(am.billamount_startdate), LPAD(MONTH(am.billamount_startdate), 2, 0)) AS '年月',
	CASE 
		WHEN am.supply_mothed = 1 THEN '直供电'
		WHEN am.supply_mothed = 2 THEN '转供电'
		ELSE ...
2025-08-19 09:11:29,600 - src.tools.integrated_sql_tools - INFO - 📥 [VANNA调用] text字段类型: sql
2025-08-19 09:11:29,601 - src.tools.integrated_sql_tools - INFO - ✅ [VANNA调用] 使用新格式解析，type=sql
2025-08-19 09:11:29,601 - src.tools.integrated_sql_tools - INFO - ✅ [VANNA调用] 成功生成SQL (总用时: 1.05秒)
2025-08-19 09:11:29,602 - src.tools.integrated_sql_tools - INFO - ✅ [VANNA调用] 生成的SQL: SELECT
	DISTINCT CONCAT(YEAR(am.billamount_startdate), LPAD(MONTH(am.billamount_startdate), 2, 0)) AS '年月',
	CASE 
		WHEN am.supply_mothed = 1 THEN '直供电'
		WHEN am.supply_mothed = 2 THEN '转供电'
		ELSE '未知'
	END AS '供电类型',
	CASE 
		WHEN am.second_fee_type = 1 THEN '自维'
		WHEN am.second_fee_type = 2 THEN '代持'
		WHEN am.second_fee_type = 3 THEN '塔维'
		ELSE '未知'
	END AS '报账点类型',
	am.preg_name as '地市',
	am.reg_name as '区县',
	eb.billaccount_code as '报账点编码',
	eb.billaccount_name as '报账点名称',
	CASE 
		WHEN am.site_type = 1 THEN '核心机楼'
		WHEN am.site_type = 2 THEN '汇聚传输站点'
		WHEN am.site_type = 3 THEN '基站'
		WHEN am.site_type = 4 THEN '室分及WLAN'
		WHEN am.site_type = 5 THEN '家客集客'
		WHEN am.site_type = 6 THEN 'IDC机房'
		WHEN am.site_type = 7 THEN '基地'
		WHEN am.site_type = 8 THEN '其他'
		WHEN am.site_type = 9 THEN '传输位置点'
		WHEN am.site_type = 10 THEN '综合位置点'
		WHEN am.site_type IS NULL THEN '-'
		ELSE '未知'
	END AS '站点类型',
	am.payment_code as '缴费单编码',
	CASE 
		WHEN am.invoice_type = 10 THEN '增值税专票'
		WHEN am.invoice_type = 21 THEN '增值税专票'
		WHEN am.invoice_type = 22 THEN '增值税专票'
		WHEN am.invoice_type = 23 THEN '增值税专票'
		WHEN am.invoice_type = 24 THEN '增值税专票'
		WHEN am.invoice_type = 25 THEN '增值税专票'
		WHEN am.invoice_type = 29 THEN '增值税专票'
		WHEN am.invoice_type = 12 THEN '增值税普票'
		WHEN am.invoice_type = 26 THEN '增值税普票'
		WHEN am.invoice_type = 31 THEN '增值税普票'
		WHEN am.invoice_type = 11 THEN '收据'
		WHEN am.invoice_type = 13 THEN '收据'
		WHEN am.invoice_type = 27 THEN '收据'
		WHEN am.invoice_type = 30 THEN '收据'
		WHEN am.invoice_type = 28 THEN '收据'
		ELSE '未知'
	END AS '票据类型归类',
	CASE 
		WHEN am.invoice_type = 10 THEN '增值税专票'
		WHEN am.invoice_type = 21 THEN '16%增值税专用发票'
		WHEN am.invoice_type = 22 THEN '17%增值税专用发票'
		WHEN am.invoice_type = 23 THEN '3%增值税专用发票'
		WHEN am.invoice_type = 24 THEN '6%增值税专用发票'
		WHEN am.invoice_type = 25 THEN '13%增值税专用发票'
		WHEN am.invoice_type = 29 THEN '增值税专用发票'
		WHEN am.invoice_type = 12 THEN '增值税普票'
		WHEN am.invoice_type = 26 THEN '发票'
		WHEN am.invoice_type = 31 THEN '普通发票'
		WHEN am.invoice_type = 11 THEN '收据'
		WHEN am.invoice_type = 13 THEN '收据+发票复印件+分割单'
		WHEN am.invoice_type = 27 THEN '发票复印件+分割单'
		WHEN am.invoice_type = 30 THEN '收据/白条'
		WHEN am.invoice_type = 28 THEN '同缴费票据类型'
		ELSE '未知'
	END AS '缴费单票据类型(三费)',
	round(am.bill_amount_actual, 2) as '实际报账金额(元)'
FROM
	aggr_ele_amortize_info am
INNER JOIN clean_ele_billaccount eb ON
	eb.billaccount_id = am.billaccount_id
WHERE
	YEAR(am.billamount_startdate) = 2025
	AND MONTH(am.billamount_startdate) = 7
	AND am.supply_mothed = 2
	AND am.invoice_type IN (11, 13, 27, 30)
2025-08-19 09:11:29,608 - src.tools.integrated_sql_tools - INFO - 🔧 [SQL执行] 开始执行SQL查询
2025-08-19 09:11:29,608 - src.tools.integrated_sql_tools - INFO - 🔧 [SQL执行] 数据库类型: mysql
2025-08-19 09:11:29,609 - src.tools.integrated_sql_tools - INFO - 🔧 [SQL执行] 原始SQL语句: SELECT
	DISTINCT CONCAT(YEAR(am.billamount_startdate), LPAD(MONTH(am.billamount_startdate), 2, 0)) AS '年月',
	CASE 
		WHEN am.supply_mothed = 1 THEN '直供电'
		WHEN am.supply_mothed = 2 THEN '转供电'
		ELSE '未知'
	END AS '供电类型',
	CASE 
		WHEN am.second_fee_type = 1 THEN '自维'
		WHEN am.second_fee_type = 2 THEN '代持'
		WHEN am.second_fee_type = 3 THEN '塔维'
		ELSE '未知'
	END AS '报账点类型',
	am.preg_name as '地市',
	am.reg_name as '区县',
	eb.billaccount_code as '报账点编码',
	eb.billaccount_name as '报账点名称',
	CASE 
		WHEN am.site_type = 1 THEN '核心机楼'
		WHEN am.site_type = 2 THEN '汇聚传输站点'
		WHEN am.site_type = 3 THEN '基站'
		WHEN am.site_type = 4 THEN '室分及WLAN'
		WHEN am.site_type = 5 THEN '家客集客'
		WHEN am.site_type = 6 THEN 'IDC机房'
		WHEN am.site_type = 7 THEN '基地'
		WHEN am.site_type = 8 THEN '其他'
		WHEN am.site_type = 9 THEN '传输位置点'
		WHEN am.site_type = 10 THEN '综合位置点'
		WHEN am.site_type IS NULL THEN '-'
		ELSE '未知'
	END AS '站点类型',
	am.payment_code as '缴费单编码',
	CASE 
		WHEN am.invoice_type = 10 THEN '增值税专票'
		WHEN am.invoice_type = 21 THEN '增值税专票'
		WHEN am.invoice_type = 22 THEN '增值税专票'
		WHEN am.invoice_type = 23 THEN '增值税专票'
		WHEN am.invoice_type = 24 THEN '增值税专票'
		WHEN am.invoice_type = 25 THEN '增值税专票'
		WHEN am.invoice_type = 29 THEN '增值税专票'
		WHEN am.invoice_type = 12 THEN '增值税普票'
		WHEN am.invoice_type = 26 THEN '增值税普票'
		WHEN am.invoice_type = 31 THEN '增值税普票'
		WHEN am.invoice_type = 11 THEN '收据'
		WHEN am.invoice_type = 13 THEN '收据'
		WHEN am.invoice_type = 27 THEN '收据'
		WHEN am.invoice_type = 30 THEN '收据'
		WHEN am.invoice_type = 28 THEN '收据'
		ELSE '未知'
	END AS '票据类型归类',
	CASE 
		WHEN am.invoice_type = 10 THEN '增值税专票'
		WHEN am.invoice_type = 21 THEN '16%增值税专用发票'
		WHEN am.invoice_type = 22 THEN '17%增值税专用发票'
		WHEN am.invoice_type = 23 THEN '3%增值税专用发票'
		WHEN am.invoice_type = 24 THEN '6%增值税专用发票'
		WHEN am.invoice_type = 25 THEN '13%增值税专用发票'
		WHEN am.invoice_type = 29 THEN '增值税专用发票'
		WHEN am.invoice_type = 12 THEN '增值税普票'
		WHEN am.invoice_type = 26 THEN '发票'
		WHEN am.invoice_type = 31 THEN '普通发票'
		WHEN am.invoice_type = 11 THEN '收据'
		WHEN am.invoice_type = 13 THEN '收据+发票复印件+分割单'
		WHEN am.invoice_type = 27 THEN '发票复印件+分割单'
		WHEN am.invoice_type = 30 THEN '收据/白条'
		WHEN am.invoice_type = 28 THEN '同缴费票据类型'
		ELSE '未知'
	END AS '缴费单票据类型(三费)',
	round(am.bill_amount_actual, 2) as '实际报账金额(元)'
FROM
	aggr_ele_amortize_info am
INNER JOIN clean_ele_billaccount eb ON
	eb.billaccount_id = am.billaccount_id
WHERE
	YEAR(am.billamount_startdate) = 2025
	AND MONTH(am.billamount_startdate) = 7
	AND am.supply_mothed = 2
	AND am.invoice_type IN (11, 13, 27, 30)
2025-08-19 09:11:29,659 - src.tools.integrated_sql_tools - INFO - 📊 [SQL工具] ✅ SQL查询完成，返回 4 条记录
2025-08-19 09:11:29,660 - src.agents.components.tool_orchestrator - INFO - ✅ [工具编排] 步骤 3 执行成功
2025-08-19 09:11:29,660 - src.agents.components.tool_orchestrator - INFO - 🔧 [工具编排] 执行步骤 4/5: 查询已报账缴费单超过合同约定单价详情
2025-08-19 09:11:29,661 - src.tools.integrated_sql_tools - INFO - 🔧 [VANNA调用] 开始生成SQL
2025-08-19 09:11:29,662 - src.tools.integrated_sql_tools - INFO - 🔧 [VANNA调用] 传递的问题: '2025年7月风险指标已报账缴费单超过合同约定单价情况'
2025-08-19 09:11:29,662 - src.tools.integrated_sql_tools - INFO - 🔧 [VANNA调用] API地址: http://10.12.22.20:5000/api/v0/generate_sql
2025-08-19 09:11:29,663 - src.tools.integrated_sql_tools - INFO - 🌐 [VANNA调用] 发送API请求...
2025-08-19 09:11:29,664 - src.tools.integrated_sql_tools - INFO - 🌐 [VANNA调用] 请求参数: question='2025年7月风险指标已报账缴费单超过合同约定单价情况'
2025-08-19 09:11:29,664 - src.tools.integrated_sql_tools - INFO - 🌐 [VANNA调用] 请求头: {'Accept': 'application/json, text/plain, */*', 'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6', 'Cache-Control': 'no-cache', 'Connection': 'keep-alive', 'Content-Type': 'application/json', 'X-API-Key': 'vn_live_8f4b5c2937e6d4a1b8f3e2c9d8a7b6e5f4c3b2a1', 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
2025-08-19 09:11:29,665 - src.tools.integrated_sql_tools - INFO - 🌐 [VANNA调用] 完整URL: http://10.12.22.20:5000/api/v0/generate_sql?question=2025年7月风险指标已报账缴费单超过合同约定单价情况
2025-08-19 09:11:30,595 - src.tools.integrated_sql_tools - INFO - 🌐 [VANNA调用] API响应状态码: 200 (请求用时: 0.93秒)
2025-08-19 09:11:30,596 - src.tools.integrated_sql_tools - INFO - 📥 [VANNA调用] API返回数据结构: ['id', 'text', 'type']
2025-08-19 09:11:30,597 - src.tools.integrated_sql_tools - INFO - 📥 [VANNA调用] 返回的text字段预览: SELECT
    CONCAT(2025,'-', LPAD(7, 2, 0)) AS '年月',
    CASE t.billamount_state 
        WHEN -2 THEN '未汇总'
        WHEN -1 THEN '未推送'
        WHEN 0 THEN '已推送'
        WHEN 1 THEN '财务审核中'
        WHE...
2025-08-19 09:11:30,598 - src.tools.integrated_sql_tools - INFO - 📥 [VANNA调用] text字段类型: sql
2025-08-19 09:11:30,599 - src.tools.integrated_sql_tools - INFO - ✅ [VANNA调用] 使用新格式解析，type=sql
2025-08-19 09:11:30,599 - src.tools.integrated_sql_tools - INFO - ✅ [VANNA调用] 成功生成SQL (总用时: 0.94秒)
2025-08-19 09:11:30,600 - src.tools.integrated_sql_tools - INFO - ✅ [VANNA调用] 生成的SQL: SELECT
    CONCAT(2025,'-', LPAD(7, 2, 0)) AS '年月',
    CASE t.billamount_state 
        WHEN -2 THEN '未汇总'
        WHEN -1 THEN '未推送'
        WHEN 0 THEN '已推送'
        WHEN 1 THEN '财务审核中'
        WHEN 2 THEN '财务已审核'
        WHEN 8 THEN '财务退回'
        ELSE '-'
    END AS '推送状态',
    t.billamount_code as '汇总单编码',
    t.preg_name as '所属地市',
    t.reg_name as '所属区县',
    t.billaccount_code as '报账点编码',
    t.payment_code as '缴费单编码',
    t.payment_days as '缴费单缴费天数',
    t.cover_days as '当月缴费天数',
    t.price_actual as '电费不含税金额',
    t.total_degree_actual as '用电量',
    t.payment_price as '缴费单电价',
    t.max_contract_price as '合同电价（最高）'
FROM
    (
        SELECT
            eba.billamount_state,
            eba.billamount_code,
            ceb.preg_name,
            ceb.reg_name,
            ceb.billaccount_code,
            ep.payment_code,
            ( ep.price_actual / ep.total_degree_actual ) AS payment_price,
            IF
                (
                        cec.price_type = 0,
                        GREATEST(
                                SUBSTRING_INDEX( cec.elecontract_price, '|', 1 ),
                                SUBSTRING_INDEX( SUBSTRING_INDEX( cec.elecontract_price, '|', 2 ), '|',-1 ),
                                SUBSTRING_INDEX( SUBSTRING_INDEX( cec.elecontract_price, '|', 3 ), '|',-1 ),
                                SUBSTRING_INDEX( SUBSTRING_INDEX( cec.elecontract_price, '|', 4 ), '|',-1 ),
                                SUBSTRING_INDEX( SUBSTRING_INDEX( cec.elecontract_price, '|', 5 ), '|',-1 ),
                                SUBSTRING_INDEX( SUBSTRING_INDEX( cec.elecontract_price, '|', 6 ), '|',-1 ),
                                SUBSTRING_INDEX( SUBSTRING_INDEX( cec.elecontract_price, '|', 7 ), '|',-1 ),
                                SUBSTRING_INDEX( SUBSTRING_INDEX( cec.elecontract_price, '|', 8 ), '|',-1 )
                            ),
                        GREATEST( cec.flat_price, cec.peak_price, cec.valley_price, cec.top_price )
                ) AS max_contract_price,
            ep.bill_amount_actual,
            (DATEDIFF(ep.billamount_enddate,ep.billamount_startdate)+1) AS payment_days,
            (DATEDIFF(
                     IF(ep.billamount_enddate > DATE_SUB(DATE_ADD(CONCAT(2025,'-', LPAD(7, 2, 0),'-01'),INTERVAL 1 MONTH),INTERVAL 1 DAY),DATE_SUB(DATE_ADD(CONCAT(2025,'-', LPAD(7, 2, 0),'-01'),INTERVAL 1 MONTH),INTERVAL 1 DAY),ep.billamount_enddate),
                     IF(ep.billamount_startdate > CONCAT(2025,'-', LPAD(7, 2, 0),'-01'),ep.billamount_startdate,CONCAT(2025,'-', LPAD(7, 2, 0),'-01')))
                +1)AS cover_days,
            ep.price_actual,
            ep.total_degree_actual
        FROM
            clean_ele_payment AS ep
                INNER JOIN clean_ele_contract AS cec ON cec.elecontract_id = ep.elecontract_id
                INNER JOIN ele_billamount AS eba ON eba.billamount_id = ep.billamount_id
                INNER JOIN clean_ele_billaccount AS ceb ON ceb.billaccount_id = ep.billaccount_id
        WHERE
            cec.is_include_all = 0
          AND eba.billamount_state = 2
          AND CONCAT(2025,'-', LPAD(7, 2, 0)) BETWEEN DATE_FORMAT(ep.billamount_startdate , '%Y-%m') AND DATE_FORMAT(ep.billamount_enddate , '%Y-%m')
        GROUP BY
            ep.payment_code
    ) AS t
WHERE
    t.payment_price > t.max_contract_price
2025-08-19 09:11:30,603 - src.tools.integrated_sql_tools - INFO - 🔧 [SQL执行] 开始执行SQL查询
2025-08-19 09:11:30,604 - src.tools.integrated_sql_tools - INFO - 🔧 [SQL执行] 数据库类型: mysql
2025-08-19 09:11:30,605 - src.tools.integrated_sql_tools - INFO - 🔧 [SQL执行] 原始SQL语句: SELECT
    CONCAT(2025,'-', LPAD(7, 2, 0)) AS '年月',
    CASE t.billamount_state 
        WHEN -2 THEN '未汇总'
        WHEN -1 THEN '未推送'
        WHEN 0 THEN '已推送'
        WHEN 1 THEN '财务审核中'
        WHEN 2 THEN '财务已审核'
        WHEN 8 THEN '财务退回'
        ELSE '-'
    END AS '推送状态',
    t.billamount_code as '汇总单编码',
    t.preg_name as '所属地市',
    t.reg_name as '所属区县',
    t.billaccount_code as '报账点编码',
    t.payment_code as '缴费单编码',
    t.payment_days as '缴费单缴费天数',
    t.cover_days as '当月缴费天数',
    t.price_actual as '电费不含税金额',
    t.total_degree_actual as '用电量',
    t.payment_price as '缴费单电价',
    t.max_contract_price as '合同电价（最高）'
FROM
    (
        SELECT
            eba.billamount_state,
            eba.billamount_code,
            ceb.preg_name,
            ceb.reg_name,
            ceb.billaccount_code,
            ep.payment_code,
            ( ep.price_actual / ep.total_degree_actual ) AS payment_price,
            IF
                (
                        cec.price_type = 0,
                        GREATEST(
                                SUBSTRING_INDEX( cec.elecontract_price, '|', 1 ),
                                SUBSTRING_INDEX( SUBSTRING_INDEX( cec.elecontract_price, '|', 2 ), '|',-1 ),
                                SUBSTRING_INDEX( SUBSTRING_INDEX( cec.elecontract_price, '|', 3 ), '|',-1 ),
                                SUBSTRING_INDEX( SUBSTRING_INDEX( cec.elecontract_price, '|', 4 ), '|',-1 ),
                                SUBSTRING_INDEX( SUBSTRING_INDEX( cec.elecontract_price, '|', 5 ), '|',-1 ),
                                SUBSTRING_INDEX( SUBSTRING_INDEX( cec.elecontract_price, '|', 6 ), '|',-1 ),
                                SUBSTRING_INDEX( SUBSTRING_INDEX( cec.elecontract_price, '|', 7 ), '|',-1 ),
                                SUBSTRING_INDEX( SUBSTRING_INDEX( cec.elecontract_price, '|', 8 ), '|',-1 )
                            ),
                        GREATEST( cec.flat_price, cec.peak_price, cec.valley_price, cec.top_price )
                ) AS max_contract_price,
            ep.bill_amount_actual,
            (DATEDIFF(ep.billamount_enddate,ep.billamount_startdate)+1) AS payment_days,
            (DATEDIFF(
                     IF(ep.billamount_enddate > DATE_SUB(DATE_ADD(CONCAT(2025,'-', LPAD(7, 2, 0),'-01'),INTERVAL 1 MONTH),INTERVAL 1 DAY),DATE_SUB(DATE_ADD(CONCAT(2025,'-', LPAD(7, 2, 0),'-01'),INTERVAL 1 MONTH),INTERVAL 1 DAY),ep.billamount_enddate),
                     IF(ep.billamount_startdate > CONCAT(2025,'-', LPAD(7, 2, 0),'-01'),ep.billamount_startdate,CONCAT(2025,'-', LPAD(7, 2, 0),'-01')))
                +1)AS cover_days,
            ep.price_actual,
            ep.total_degree_actual
        FROM
            clean_ele_payment AS ep
                INNER JOIN clean_ele_contract AS cec ON cec.elecontract_id = ep.elecontract_id
                INNER JOIN ele_billamount AS eba ON eba.billamount_id = ep.billamount_id
                INNER JOIN clean_ele_billaccount AS ceb ON ceb.billaccount_id = ep.billaccount_id
        WHERE
            cec.is_include_all = 0
          AND eba.billamount_state = 2
          AND CONCAT(2025,'-', LPAD(7, 2, 0)) BETWEEN DATE_FORMAT(ep.billamount_startdate , '%Y-%m') AND DATE_FORMAT(ep.billamount_enddate , '%Y-%m')
        GROUP BY
            ep.payment_code
    ) AS t
WHERE
    t.payment_price > t.max_contract_price
2025-08-19 09:11:30,678 - src.tools.integrated_sql_tools - INFO - 📊 [SQL工具] ✅ SQL查询完成，返回 4 条记录
2025-08-19 09:11:30,679 - src.agents.components.tool_orchestrator - INFO - ✅ [工具编排] 步骤 4 执行成功
2025-08-19 09:11:30,679 - src.agents.components.tool_orchestrator - INFO - 🔧 [工具编排] 执行步骤 5/5: 搜索风险管理改进建议
2025-08-19 09:11:30,680 - src.tools.knowledge_search_tools - INFO - 📚 [知识库] 🔍 正在查询知识库: 网络电费风险管理 改进建议
2025-08-19 09:11:30,681 - src.tools.knowledge_search_tools - INFO - 📚 [知识库] API地址: http://10.12.22.20:8081/v1/workflows/run
2025-08-19 09:11:30,681 - src.tools.knowledge_search_tools - INFO - 🌐 [知识库] 发送API请求...
2025-08-19 09:11:30,682 - src.tools.knowledge_search_tools - INFO - 📤 [知识库] 请求体: {'inputs': {'query': '网络电费风险管理 改进建议'}, 'response_mode': 'blocking', 'user': 'sql-agent'}
2025-08-19 09:11:36,317 - src.tools.knowledge_search_tools - INFO - 🌐 [知识库] API响应状态码: 200 (请求用时: 5.63秒)
2025-08-19 09:11:36,318 - src.tools.knowledge_search_tools - INFO - 📥 [知识库] API返回数据结构: ['task_id', 'workflow_run_id', 'data']
2025-08-19 09:11:36,318 - src.tools.knowledge_search_tools - INFO - 📚 [知识库] ✅ 知识库查询成功，找到 4 条相关信息 (总用时: 5.64秒，请求: 5.63秒，解析: 0.001秒，格式化: 0.000秒)
2025-08-19 09:11:36,319 - src.tools.knowledge_search_tools - INFO - 📄 [知识库结果1] 标题: 费控助手指标计算逻辑.md
2025-08-19 09:11:36,319 - src.tools.knowledge_search_tools - INFO - 📄 [知识库结果1] 相关度: 1.0
2025-08-19 09:11:36,320 - src.tools.knowledge_search_tools - INFO - 📄 [知识库结果1] 内容预览: 指标名：用电成本
别名：单位用电成本 
计算逻辑：用电成本=电费/电量
2025-08-19 09:11:36,321 - src.tools.knowledge_search_tools - INFO - 📄 [知识库结果2] 标题: 2024年网络条线提质增效专题部署材料V3.pdf
2025-08-19 09:11:36,321 - src.tools.knowledge_search_tools - INFO - 📄 [知识库结果2] 相关度: 1.0
2025-08-19 09:11:36,321 - src.tools.knowledge_search_tools - INFO - 📄 [知识库结果2] 内容预览: 9

3.3 网络电费：争取优惠政策，加强网络电费单价管控

1-6月，全省单位用电成本0.58元/度，相比2023年平均上涨36.6%由于直供电优惠政策取消，电费成本压力较大需进一步加强

转供电治...
2025-08-19 09:11:36,322 - src.tools.knowledge_search_tools - INFO - 📄 [知识库结果3] 标题: 集团三费系统常见问题答疑.xlsx
2025-08-19 09:11:36,323 - src.tools.knowledge_search_tools - INFO - 📄 [知识库结果3] 相关度: 1.0
2025-08-19 09:11:36,323 - src.tools.knowledge_search_tools - INFO - 📄 [知识库结果3] 内容预览: 系统常见公式":"发票税额=调整后电费税额（实际推送电费税额）+其他费用税金汇总金额"
2025-08-19 09:11:36,324 - src.agents.components.tool_orchestrator - INFO - ✅ [工具编排] 步骤 5 执行成功
2025-08-19 09:11:36,324 - src.agents.components.tool_orchestrator - INFO - 🏁 [工具编排] 执行完成，成功 5/5 个步骤
2025-08-19 09:11:36,325 - src.agents.analysis_agent_v2 - INFO - 🔧 [AnalysisAgentV2] 工具编排完成 (用时: 9.71秒)
2025-08-19 09:11:36,325 - src.agents.components.response_formatter - INFO - 📝 [结果整合] 开始格式化响应，工具结果数: 5
2025-08-19 09:11:36,326 - src.agents.components.response_formatter - INFO - ✅ [结果整合] 响应格式化完成，包含表格: True
2025-08-19 09:11:36,326 - src.agents.analysis_agent_v2 - INFO - 📝 [AnalysisAgentV2] 结果整合完成 (用时: 0.00秒)
2025-08-19 09:11:36,327 - src.agents.analysis_agent_v2 - INFO - ✅ [AnalysisAgentV2] 处理完成 (总用时: 10.23秒)
2025-08-19 09:11:36,363 - __main__ - INFO - 🚀 [API调用] /v1/chat/completions 端点被调用
2025-08-19 09:11:36,365 - __main__ - INFO - 👤 [用户输入] '2025年4月电费总额是多少'
2025-08-19 09:11:36,365 - __main__ - INFO - 📚 [对话历史] 0 条历史记录
2025-08-19 09:11:36,366 - __main__ - INFO - 🔧 [最终消息] Agent将处理: '2025年4月电费总额是多少'
2025-08-19 09:11:36,367 - src.agents.analysis_agent_v2 - INFO - 🤖 [AnalysisAgentV2] 开始处理用户消息: 2025年4月电费总额是多少
2025-08-19 09:11:36,367 - src.agents.vector_enhanced_agent - INFO - 🎯 [问题提取] 未找到特定格式，使用原始消息: 2025年4月电费总额是多少
2025-08-19 09:11:36,368 - src.agents.vector_enhanced_agent - INFO - 🔍 [向量搜索] 搜索查询: 2025年4月电费总额是多少
2025-08-19 09:11:36,369 - src.core.prompt_vectorstore_manager - INFO - 🔍 开始搜索复杂提示词，数据库中共有 8 条数据
2025-08-19 09:11:36,370 - src.core.prompt_vectorstore_manager - INFO - 🔍 搜索查询: '2025年4月电费总额是多少', k=3, score_threshold=0.95
2025-08-19 09:11:36,679 - src.core.prompt_vectorstore_manager - INFO - 🔍 向量搜索返回 8 个原始结果
2025-08-19 09:11:36,679 - src.core.prompt_vectorstore_manager - INFO - 🔍 搜索到 0 个匹配的复杂提示词
2025-08-19 09:11:36,680 - src.agents.vector_enhanced_agent - INFO - ❌ [向量搜索] 未找到匹配的复杂提示词
2025-08-19 09:11:36,680 - src.agents.components.intent_analyzer - INFO - 🔍 [意图分析] 开始分析用户输入: 2025年4月电费总额是多少
2025-08-19 09:11:36,680 - src.agents.components.intent_analyzer - INFO - 🎯 [意图分析] 提取的用户问题: 2025年4月电费总额是多少
2025-08-19 09:11:36,681 - src.agents.components.intent_analyzer - INFO - ✅ [意图分析] 分析完成: simple_query, 置信度: 0.800, 多步骤: False
2025-08-19 09:11:36,681 - src.agents.analysis_agent_v2 - INFO - 🎯 [AnalysisAgentV2] 意图分析完成 (用时: 0.31秒)
2025-08-19 09:11:36,681 - src.agents.components.tool_orchestrator - INFO - 📋 [工具编排] 开始规划执行计划，意图类型: simple_query
2025-08-19 09:11:36,682 - src.agents.components.tool_orchestrator - INFO - ✅ [工具编排] 执行计划完成，共 1 个步骤
2025-08-19 09:11:36,682 - src.agents.components.tool_orchestrator - INFO - 🚀 [工具编排] 开始执行计划，共 1 个步骤
2025-08-19 09:11:36,682 - src.agents.components.tool_orchestrator - INFO - 🔧 [工具编排] 执行步骤 1/1: 查询: 2025年4月电费总额是多少
2025-08-19 09:11:36,683 - src.tools.integrated_sql_tools - INFO - 🔧 [VANNA调用] 开始生成SQL
2025-08-19 09:11:36,683 - src.tools.integrated_sql_tools - INFO - 🔧 [VANNA调用] 传递的问题: '2025年4月电费总额是多少'
2025-08-19 09:11:36,684 - src.tools.integrated_sql_tools - INFO - 🔧 [VANNA调用] API地址: http://10.12.22.20:5000/api/v0/generate_sql
2025-08-19 09:11:36,684 - src.tools.integrated_sql_tools - INFO - 🌐 [VANNA调用] 发送API请求...
2025-08-19 09:11:36,684 - src.tools.integrated_sql_tools - INFO - 🌐 [VANNA调用] 请求参数: question='2025年4月电费总额是多少'
2025-08-19 09:11:36,685 - src.tools.integrated_sql_tools - INFO - 🌐 [VANNA调用] 请求头: {'Accept': 'application/json, text/plain, */*', 'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6', 'Cache-Control': 'no-cache', 'Connection': 'keep-alive', 'Content-Type': 'application/json', 'X-API-Key': 'vn_live_8f4b5c2937e6d4a1b8f3e2c9d8a7b6e5f4c3b2a1', 'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
2025-08-19 09:11:36,685 - src.tools.integrated_sql_tools - INFO - 🌐 [VANNA调用] 完整URL: http://10.12.22.20:5000/api/v0/generate_sql?question=2025年4月电费总额是多少
2025-08-19 09:11:47,715 - src.tools.integrated_sql_tools - INFO - 🌐 [VANNA调用] API响应状态码: 200 (请求用时: 11.03秒)
2025-08-19 09:11:47,717 - src.tools.integrated_sql_tools - INFO - 📥 [VANNA调用] API返回数据结构: ['id', 'text', 'type']
2025-08-19 09:11:47,717 - src.tools.integrated_sql_tools - INFO - 📥 [VANNA调用] 返回的text字段预览: SELECT
    SUM(IFNULL(loss_bill_amount_straight, 0) + IFNULL(bill_amount_straight, 0) + IFNULL(loss_bill_amount_transfer, 0) + IFNULL(bill_amount_transfer, 0) + IFNULL(bill_amount_is_include, 0)) AS '...
2025-08-19 09:11:47,718 - src.tools.integrated_sql_tools - INFO - 📥 [VANNA调用] text字段类型: sql
2025-08-19 09:11:47,718 - src.tools.integrated_sql_tools - INFO - ✅ [VANNA调用] 使用新格式解析，type=sql
2025-08-19 09:11:47,719 - src.tools.integrated_sql_tools - INFO - ✅ [VANNA调用] 成功生成SQL (总用时: 11.04秒)
2025-08-19 09:11:47,719 - src.tools.integrated_sql_tools - INFO - ✅ [VANNA调用] 生成的SQL: SELECT
    SUM(IFNULL(loss_bill_amount_straight, 0) + IFNULL(bill_amount_straight, 0) + IFNULL(loss_bill_amount_transfer, 0) + IFNULL(bill_amount_transfer, 0) + IFNULL(bill_amount_is_include, 0)) AS '电费总额',
    '万元' AS '单位'
FROM
    analysis_reference_ele
WHERE
    on_year = 2025
    AND on_month = 4
    AND is_year_to_month = 0
    AND rpt_type = 2;
2025-08-19 09:11:47,720 - src.tools.integrated_sql_tools - INFO - 🔧 [SQL执行] 开始执行SQL查询
2025-08-19 09:11:47,721 - src.tools.integrated_sql_tools - INFO - 🔧 [SQL执行] 数据库类型: mysql
2025-08-19 09:11:47,722 - src.tools.integrated_sql_tools - INFO - 🔧 [SQL执行] 原始SQL语句: SELECT
    SUM(IFNULL(loss_bill_amount_straight, 0) + IFNULL(bill_amount_straight, 0) + IFNULL(loss_bill_amount_transfer, 0) + IFNULL(bill_amount_transfer, 0) + IFNULL(bill_amount_is_include, 0)) AS '电费总额',
    '万元' AS '单位'
FROM
    analysis_reference_ele
WHERE
    on_year = 2025
    AND on_month = 4
    AND is_year_to_month = 0
    AND rpt_type = 2;
2025-08-19 09:11:47,801 - src.tools.integrated_sql_tools - INFO - 📊 [SQL工具] ✅ SQL查询完成，返回 1 条记录
2025-08-19 09:11:47,802 - src.agents.components.tool_orchestrator - INFO - ✅ [工具编排] 步骤 1 执行成功
2025-08-19 09:11:47,802 - src.agents.components.tool_orchestrator - INFO - 🏁 [工具编排] 执行完成，成功 1/1 个步骤
2025-08-19 09:11:47,803 - src.agents.analysis_agent_v2 - INFO - 🔧 [AnalysisAgentV2] 工具编排完成 (用时: 11.12秒)
2025-08-19 09:11:47,803 - src.agents.components.response_formatter - INFO - 📝 [结果整合] 开始格式化响应，工具结果数: 1
2025-08-19 09:11:47,804 - src.agents.components.response_formatter - INFO - ✅ [结果整合] 响应格式化完成，包含表格: True
2025-08-19 09:11:47,805 - src.agents.analysis_agent_v2 - INFO - 📝 [AnalysisAgentV2] 结果整合完成 (用时: 0.00秒)
2025-08-19 09:11:47,806 - src.agents.analysis_agent_v2 - INFO - ✅ [AnalysisAgentV2] 处理完成 (总用时: 11.44秒)
